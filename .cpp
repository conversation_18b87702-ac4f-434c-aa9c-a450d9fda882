#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <SPI.h>
#include <vector>
#include <map>
#include <math.h>

// 添加 min 宏定义以解决所有 min 未定义的问题
#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif
#ifndef max
#define max(a, b) ((a) > (b) ? (a) : (b))
#endif

// ====================== OLED 显示设置 ======================
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// ====================== 按钮引脚定义 ======================
const int leftButton = PA7;     // 左移动/菜单导航
const int rightButton = PA12;   // 右移动/菜单导航
const int rotateButton = PA14;  // 旋转/确认选择
const int downButton = PA30;    // 加速下落/特殊功能

// ====================== 游戏状态管理 ======================
enum GameState { MENU, TETRIS, BALL_GAME, CUBE_GAME, BALL_MENU, TIMER_GAME, CALCULATOR, INFO }; // 添加INFO状态
GameState currentGame = MENU;

// ====================== 弹球游戏难度设置 ======================
const char* difficultyNames[3] = {"Easy", "Medium", "Hard"};
int difficultySelection = 1; // 默认选择中等难度
float difficultyFactors[3] = {0.8f, 1.0f, 1.5f}; // 难度系数

// ====================== 弹球游戏状态枚举 ======================
enum BallState { PLAYING, BOUNCING, GAME_OVER, PAUSED };

// ====================== 计时器状态 ======================
enum TimerState { TIMER_STOPPED, TIMER_RUNNING, TIMER_PAUSED };
TimerState timerState = TIMER_STOPPED;

// 计时变量
unsigned long timerStartTime = 0;
unsigned long timerElapsedTime = 0;
unsigned long timerLastUpdate = 0;

// 按钮状态标志
bool timerStartButtonHandled = true;
bool timerResetButtonHandled = true;

// ====================== 菜单系统 ======================
// 添加INFO选项（共6个选项）
const char* gameNames[6] = {"Tetris", "Ball Game", "3D Test", "Timer", "Calculator", "Info"};
const uint8_t* gameIcons[6] = {
  // Tetris 图标 (8x8)
  new uint8_t[8]{0x00,0x7E,0x7E,0x18,0x18,0x18,0x18,0x00},
  // Ball Game 图标 (8x8)
  new uint8_t[8]{0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C},
  // 3D Test 图标 (8x8)
  new uint8_t[8]{0x18, 0x24, 0x42, 0x81, 0x81, 0x42, 0x24, 0x18},
  // Timer 图标 (8x8)
  new uint8_t[8]{0x3C, 0x42, 0x99, 0xA5, 0xA1, 0x99, 0x42, 0x3C},
  // Calculator 图标 (8x8)
  new uint8_t[8]{0x7F, 0x41, 0x5D, 0x41, 0x5D, 0x41, 0x41, 0x7F},
  // Info 图标 (8x8) - 新增的Info图标
  new uint8_t[8]{0x3C,0x42,0x99,0x81,0x99,0x99,0x5A,0x3C}
};

int menuSelection = 0;
int menuPage = 0; // 添加菜单页面变量，0表示第一页，1表示第二页
const int itemsPerPage = 6; // 每页显示的项数

// 菜单动画变量（新增缓动参数）
int menuScrollOffset = 0;       // 当前滚动偏移量
int targetScrollOffset = 0;     // 目标滚动偏移量
float scrollVelocity = 0;       // 滚动速度（用于缓动计算）
const float EASE_SPEED = 0.15;   // 缓动速度系数（控制动画平滑度）
unsigned long lastMenuAnimationTime = 0;
const int MENU_ANIMATION_SPEED = 5;

// ====================== 全局游戏变量 ======================
float angleX = 0; // 立方体旋转角度 X
float angleY = 0; // 立方体旋转角度 Y
float angleZ = 0; // 立方体旋转角度 Z
BallState ballState = PLAYING; // 弹球游戏状态

// 新增帧率显示相关变量
int menuFPS = 0;                  // 菜单帧率
unsigned long lastMenuFPSTime = 0; // 上次更新菜单FPS的时间
int menuFrameCount = 0;           // 菜单帧计数

// ====================== 计算器相关变量 ======================
// 24键虚拟键盘布局（4行6列）
const char* buttons[24] = {
  "C",   "√",  "%",   "/",   "*",   "-",
  "7",   "8",   "9",   "(",   ")",   "+",
  "4",   "5",   "6",   "DEL", "+/-", "=",
  "1",   "2",   "3",   "0",   ".",   "π"   // 将最后一个等于号改为π
};

// 重新调整布局以适应屏幕
const uint8_t btnWidth = 20;    // 按钮宽度
const uint8_t btnHeight = 10;   // 按钮高度
const uint8_t btnPadding = 1;   // 按钮间距
const uint8_t startX = 2;       // 键盘起始X坐标
const uint8_t startY = 16;      // 键盘起始Y坐标（从第16像素开始）

// 计算器状态
String calcInput = "";
float operand1 = 0;
char operation = '\0';
bool newInput = true;
bool operationSet = false;

// 当前选中的按钮索引 (0-23)
int selectedButton = 0;

// 按键状态
bool lastSelect1State = HIGH;
bool lastSelect2State = HIGH;
bool lastDownState = HIGH;
bool lastConfirmState = HIGH;
unsigned long lastDebounceTime = 0;
const unsigned long debounceDelay = 50;

// ====================== 菜单系统函数 ======================
void drawMenuIcon(int x, int y, const uint8_t* icon, uint16_t color) {
  for (int i = 0; i < 8; i++) {         // 遍历图标的8行
    uint8_t row = icon[i];              // 获取当前行的字节数据
    for (int j = 0; j < 8; j++) {       // 遍历当前行的8个像素
      if (row & (1 << j)) {             // 检查该像素是否需要点亮（1表示亮）
        display.drawPixel(x + j, y + i, color);  // 使用传入的颜色绘制像素
      }
    }
  }
}

void renderMenu() {
  display.clearDisplay();
  
  // 显示帧率 (FPS) 在左上角
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  display.print("F:");
  display.setCursor(15,0);
  display.print(menuFPS);
  
  // 标题
  display.setTextSize(1);
  display.setCursor(35, 1);
  display.print("GAME MENU");
  
  // 分隔线
  display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);
  
  // 计算当前页面的起始索引
  int startIdx = menuPage * itemsPerPage;
  
  // 游戏选项（每页显示6个）
  for (int i = 0; i < itemsPerPage; i++) {
    int gameIdx = startIdx + i;
    if (gameIdx >= 6) break; // 防止超出范围（现在有6个选项）
    
    // 使用四舍五入后的整数值进行渲染
    int yPos = 15 + i*15 - static_cast<int>(menuScrollOffset + 0.5f);
    
    // 只绘制可见项
    if (yPos >= 12 && yPos < SCREEN_HEIGHT - 10) {
      if (gameIdx == menuSelection) {
        // 高亮背景
        display.fillRoundRect(5, yPos - 1, SCREEN_WIDTH-10, 13, 3, SSD1306_WHITE);
        display.setTextColor(SSD1306_BLACK);
        
        // 绘制选中指示器（三角形）
        display.fillTriangle(SCREEN_WIDTH - 15, yPos + 5, 
                            SCREEN_WIDTH - 10, yPos + 2, 
                            SCREEN_WIDTH - 10, yPos + 8, SSD1306_BLACK);
        
        // 绘制黑色图标（关键修改：传递SSD1306_BLACK）
        uint16_t iconColor = SSD1306_BLACK; // 选中项图标黑色
        drawMenuIcon(10, yPos + 2, gameIcons[gameIdx], iconColor);
      } else {
        display.setTextColor(SSD1306_WHITE);
        
        // 绘制白色图标（关键修改：传递SSD1306_WHITE）
        uint16_t iconColor = SSD1306_WHITE; // 非选中项图标白色
        drawMenuIcon(10, yPos + 2, gameIcons[gameIdx], iconColor);
      }
      
      display.setCursor(25, yPos+2);
      display.print(gameNames[gameIdx]);
    }
  }
  
  // 操作提示
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(5, SCREEN_HEIGHT-7);  // 第一行提示
  display.print("UP:Sel D:Page");
  
  display.display();
}

// 使用平滑缓动算法的菜单动画更新
void updateMenuAnimation() {
  if (menuScrollOffset != targetScrollOffset) {
    unsigned long currentTime = millis();
    if (currentTime - lastMenuAnimationTime > MENU_ANIMATION_SPEED) {
      lastMenuAnimationTime = currentTime;
      
      // 计算当前位置到目标位置的差值
      int diff = targetScrollOffset - menuScrollOffset;
      
      // 使用缓动函数平滑移动（距离越远速度越快，接近时减速）
      if (abs(diff) > 1) {
        // 使用指数衰减公式计算平滑移动距离
        menuScrollOffset += diff * EASE_SPEED;
      } else {
        // 当距离足够小时，直接设置为目标位置，避免微小抖动
        menuScrollOffset = targetScrollOffset;
      }
    }
  }
}

void menuLoop() {
  updateMenuAnimation();
  
  // 更新菜单FPS
  menuFrameCount++;
  if (millis() - lastMenuFPSTime >= 1000) {
    menuFPS = menuFrameCount; // 修改全局变量
    menuFrameCount = 0;
    lastMenuFPSTime = millis();
  }
  
  // 处理菜单翻页
  if (digitalRead(downButton) == LOW) {
    int oldPage = menuPage;
    menuPage = (menuPage + 1) % 1;  // 在0和1之间切换
    
    // 翻到第二页时自动选择第二页第一个选项
    if (oldPage == 0 && menuPage == 1) {
      menuSelection = 3; // 第二页第一个选项的索引是3
      targetScrollOffset = 0; // 重置滚动位置
    }
    // 翻回第一页时自动选择第一页第一个选项
    else if (oldPage == 1 && menuPage == 0) {
      menuSelection = 0; // 第一页第一个选项的索引是0
      targetScrollOffset = 0; // 重置滚动位置
    }
    
    delay(200);
  }
  
  // 计算当前页面的实际索引范围
  int startIdx = menuPage * itemsPerPage;
  int endIdx = min(startIdx + itemsPerPage, 6) - 1;  // 确保不越界（现在有6个选项）
  
  // 处理菜单导航
  if (digitalRead(leftButton) == LOW) {
    menuSelection--;
    if (menuSelection < startIdx) {
      menuSelection = endIdx;
    }
    
    // 计算目标滚动位置（确保选中项在可视区域内）
    int itemPos = (menuSelection % itemsPerPage) * 15;
    targetScrollOffset = itemPos;
    
    delay(150); // 减少延迟，使响应更灵敏
  }
  
  if (digitalRead(rightButton) == LOW) {
    menuSelection++;
    if (menuSelection > endIdx) {
      menuSelection = startIdx;
    }
    
    // 计算目标滚动位置
    int itemPos = (menuSelection % itemsPerPage) * 15;
    targetScrollOffset = itemPos;
    
    delay(150); // 减少延迟，使响应更灵敏
  }

  // 处理选择
  if (digitalRead(rotateButton) == LOW) {
    if (menuSelection == 0) {
      currentGame = TETRIS;
      resetTetris();
    } else if (menuSelection == 1) {
      // 进入弹球游戏难度选择菜单
      currentGame = BALL_MENU;
      difficultySelection = 1; // 重置为默认难度
    } else if (menuSelection == 2) {
      currentGame = CUBE_GAME;
      angleX = 0;
      angleY = 0;
      angleZ = 0;
    } else if (menuSelection == 3) {
      currentGame = TIMER_GAME;
      timerState = TIMER_STOPPED;
      timerElapsedTime = 0;
    } else if (menuSelection == 4) {
      currentGame = CALCULATOR;
      // 初始化计算器状态
      calcInput = "";
      operand1 = 0;
      operation = '\0';
      newInput = true;
      operationSet = false;
      selectedButton = 0;
      calcDrawCalculator();
    } else if (menuSelection == 5) { // 新增的Info选项
      currentGame = INFO;
    }
    delay(200);
    return;
  }
  
  // 渲染菜单
  renderMenu();
}

// ====================== 弹球游戏难度选择菜单 ======================
int ballMenuScrollOffset = 0;       // 当前滚动偏移量
int ballMenuTargetScrollOffset = 0; // 目标滚动偏移量
float ballMenuScrollVelocity = 0;   // 滚动速度（用于缓动计算）
const float BALL_MENU_EASE_SPEED = 0.15; // 缓动速度系数
unsigned long lastBallMenuAnimationTime = 0;
const int BALL_MENU_ANIMATION_SPEED = 5;

void renderBallMenu() {
  display.clearDisplay();
  
  // 标题
  display.setCursor(15, 1);
  display.print("SELECT DIFFICULTY");
  
  // 分隔线
  display.drawLine(0, 10, SCREEN_WIDTH, 10, SSD1306_WHITE);
  
  // 难度选项
  for (int i = 0; i < 3; i++) {
    int yPos = 20 + i*12 - static_cast<int>(ballMenuScrollOffset + 0.5f);
    
    // 只绘制可见项
    if (yPos >= 12 && yPos < SCREEN_HEIGHT - 10) {
      if (i == difficultySelection) {
        display.fillRoundRect(30, yPos - 1, SCREEN_WIDTH-60, 13, 3, SSD1306_WHITE);
        display.setTextColor(SSD1306_BLACK);
        
        // 绘制选中指示器
        display.fillTriangle(SCREEN_WIDTH - 15, yPos + 5, 
                            SCREEN_WIDTH - 10, yPos + 2, 
                            SCREEN_WIDTH - 10, yPos + 8, SSD1306_BLACK);
      } else {
        display.setTextColor(SSD1306_WHITE);
      }
      
      display.setCursor(40, yPos+2);
      display.print(difficultyNames[i]);
    }
  }
  
  // 操作提示
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(15, SCREEN_HEIGHT-8);
  display.print("UP:Select D:Exit");
  
  display.display();
}

void updateBallMenuAnimation() {
  if (ballMenuScrollOffset != ballMenuTargetScrollOffset) {
    unsigned long currentTime = millis();
    if (currentTime - lastBallMenuAnimationTime > BALL_MENU_ANIMATION_SPEED) {
      lastBallMenuAnimationTime = currentTime;
      
      // 计算当前位置到目标位置的差值
      int diff = ballMenuTargetScrollOffset - ballMenuScrollOffset;
      
      // 使用缓动函数平滑移动（距离越远速度越快，接近时减速）
      if (abs(diff) > 1) {
        // 使用指数衰减公式计算平滑移动距离
        ballMenuScrollOffset += diff * BALL_MENU_EASE_SPEED;
      } else {
        // 当距离足够小时，直接设置为目标位置，避免微小抖动
        ballMenuScrollOffset = ballMenuTargetScrollOffset;
      }
    }
  }
}

void ballMenuLoop() {
  updateBallMenuAnimation();
  
  // 处理菜单导航
  if (digitalRead(leftButton) == LOW) {
    difficultySelection = (difficultySelection - 1 + 3) % 3;
    
    // 计算目标滚动位置
    int itemPos = (difficultySelection % 3) * 12;
    ballMenuTargetScrollOffset = itemPos;
    
    delay(150);
  }
  
  if (digitalRead(rightButton) == LOW) {
    difficultySelection = (difficultySelection + 1) % 3;
    
    // 计算目标滚动位置
    int itemPos = (difficultySelection % 3) * 12;
    ballMenuTargetScrollOffset = itemPos;
    
    delay(150);
  }

  // 处理选择
  if (digitalRead(rotateButton) == LOW) {
    currentGame = BALL_GAME;
    resetBallGame();
    delay(150);
    return;
  }
  
  // 返回主菜单
  if (digitalRead(downButton) == LOW) {
    currentGame = MENU;
    delay(150);
    return;
  }
  
  // 渲染菜单
  renderBallMenu();
}
// ====================== 计时器功能 ======================
void timerDisplayWelcomeScreen() {
  display.clearDisplay();
  
  // 显示标题
  display.setTextSize(2);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(10, 10);
  display.println("RTL8720DN");
  
  display.setCursor(30, 30);
  display.println("TimerV1.0");
  display.display();
}

void timerLoop() {
  // 处理开始/暂停按钮 (映射到右按钮)
  if (digitalRead(rightButton) == HIGH) {
    timerStartButtonHandled = false;
  } else {
    if (!timerStartButtonHandled) {
      timerStartButtonHandled = true;
      
      if (timerState == TIMER_STOPPED || timerState == TIMER_PAUSED) {
        // 开始计时
        if (timerState == TIMER_STOPPED) {
          timerElapsedTime = 0;
        }
        timerStartTime = millis();
        timerState = TIMER_RUNNING;
      } else if (timerState == TIMER_RUNNING) {
        // 暂停计时
        timerElapsedTime += millis() - timerStartTime;
        timerState = TIMER_PAUSED;
      }
    }
  }
  
  // 处理重置按钮 (映射到左按钮)
  if (digitalRead(leftButton) == HIGH) {
    timerResetButtonHandled = false;
  } else {
    if (!timerResetButtonHandled) {
      timerResetButtonHandled = true;
      timerState = TIMER_STOPPED;
      timerElapsedTime = 0;
    }
  }
  
  // 更新显示
  unsigned long currentTime = timerElapsedTime;
  
  if (timerState == TIMER_RUNNING) {
    currentTime += millis() - timerStartTime;
  }
  
  // 计算时间各部分
  unsigned int hours = currentTime / 3600000;
  unsigned int minutes = (currentTime % 3600000) / 60000;
  unsigned int seconds = (currentTime % 60000) / 1000;
  unsigned int milliseconds = currentTime % 1000;
  
  // 准备显示内容
  display.clearDisplay();
  
  // 显示标题
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(15, 5);
  display.print("RTL8720DN Timer");
  
  // 显示时间
  display.setTextSize(2);
  display.setCursor(10, 25);
  
  // 格式化时间显示
  if (hours > 0) {
    if (hours < 10) display.print("0");
    display.print(hours);
    display.print(":");
  }
  
  if (minutes < 10) display.print("0");
  display.print(minutes);
  display.print(":");
  
  if (seconds < 10) display.print("0");
  display.print(seconds);
  display.print(".");
  
  if (milliseconds < 100) display.print("0");
  if (milliseconds < 10) display.print("0");
  display.print(milliseconds);
  
  // 显示状态指示器
  display.fillRect(0, 55, 128, 9, SSD1306_BLACK);
  display.drawRect(0, 55, 128, 9, SSD1306_WHITE);
  
  if (timerState == TIMER_RUNNING) {
    display.fillRect(3, 57, 40, 5, SSD1306_WHITE);
    display.setTextColor(SSD1306_BLACK);
    display.setCursor(8, 56);
    display.print("RUN");
  } else if (timerState == TIMER_PAUSED) {
    display.fillRect(45, 57, 40, 5, SSD1306_WHITE);
    display.setTextColor(SSD1306_BLACK);
    display.setCursor(50, 56);
    display.print("PAUSE");
  } else {
    display.fillRect(87, 57, 40, 5, SSD1306_WHITE);
    display.setTextColor(SSD1306_BLACK);
    display.setCursor(92, 56);
    display.print("STOP");
  }
  display.display();
  
  // 返回菜单检测
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

// ====================== 俄罗斯方块游戏部分 ======================
const int gridWidth = 10;
const int gridHeight = 16;
const int cellSize = 4;
const int gridOffsetX = 80;
const int gridOffsetY = 0;

// 方块形状定义
const uint8_t shapes[7][4][4][4] = {
  // I 形 (0)
  {
    {{0,0,0,0}, {1,1,1,1}, {0,0,0,0}, {0,0,0,0}}, // 0°
    {{0,0,1,0}, {0,0,1,0}, {0,0,1,0}, {0,0,1,0}}, // 90°
    {{0,0,0,0}, {0,0,0,0}, {1,1,1,1}, {0,0,0,0}}, // 180°
    {{0,1,0,0}, {0,1,0,0}, {0,1,0,0}, {0,1,0,0}}  // 270°
  },
  // O 形 (1)
  {
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}}, // 0°
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}}, // 180°
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}}  // 270°
  },
  // L 形 (2)
  {
    {{0,0,0,0}, {0,1,1,1}, {0,1,0,0}, {0,0,0,0}}, // 0°
    {{0,0,1,0}, {0,0,1,0}, {0,0,1,1}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {0,0,1,0}, {1,1,1,0}, {0,0,0,0}}, // 180°
    {{0,0,0,0}, {1,1,0,0}, {0,1,0,0}, {0,1,0,0}}  // 270°
  },
  // J 形 (3)
  {
    {{0,0,0,0}, {0,1,1,1}, {0,0,0,1}, {0,0,0,0}}, // 0°
    {{0,0,1,1}, {0,0,1,0}, {0,0,1,0}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {1,0,0,0}, {1,1,1,0}, {0,0,0,0}}, // 180°
    {{0,0,1,0}, {0,0,1,0}, {0,1,1,0}, {0,0,0,0}}  // 270°
  },
  // S 形 (4)
  {
    {{0,0,0,0}, {0,1,1,0}, {1,1,0,0}, {0,0,0,0}}, // 0°
    {{0,1,0,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {0,1,1,0}, {1,1,0,0}, {0,0,0,0}}, // 180°
    {{0,1,0,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}}  // 270°
  },
  // Z 形 (5)
  {
    {{0,0,0,0}, {1,1,0,0}, {0,1,1,0}, {0,0,0,0}}, // 0°
    {{0,0,1,0}, {0,1,1,0}, {0,1,0,0}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {1,1,0,0}, {0,1,1,0}, {0,0,0,0}}, // 180°
    {{0,0,1,0}, {0,1,1,0}, {0,1,0,0}, {0,0,0,0}}  // 270°
  },
  // T 形 (6)
  {
    {{0,0,0,0}, {0,1,1,1}, {0,0,1,0}, {0,0,0,0}}, // 0°
    {{0,0,1,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}}, // 90°
    {{0,0,0,0}, {0,0,1,0}, {0,1,1,1}, {0,0,0,0}}, // 180°
    {{0,0,1,0}, {0,0,1,1}, {0,0,1,0}, {0,0,0,0}}  // 270°
  }
};

// 俄罗斯方块游戏变量
int tetrisGrid[gridHeight][gridWidth] = {0};
int currentX = 4, currentY = 0;
int currentShape = 0;
int currentRotation = 0;
unsigned long lastFall = 0;
int fallSpeed = 500;
int tetrisScore = 0;
int level = 1;
int linesCleared = 0;
bool tetrisActive = false;
int nextShape = random(7);
int nextRotation = 0;
unsigned long lastTetrisFPSTime = 0;
int tetrisFrameCount = 0;
int tetrisFPS = 0;

// 方块颜色
const uint16_t tetrisColors[7] = {
  SSD1306_WHITE,   // I
  SSD1306_WHITE,   // O
  SSD1306_WHITE,   // L
  SSD1306_WHITE,   // J
  SSD1306_WHITE,   // S
  SSD1306_WHITE,   // Z
  SSD1306_WHITE    // T
};

// 俄罗斯方块辅助函数
void drawSquareBlock(int x, int y, int color) {
  display.fillRect(x, y, cellSize, cellSize, color);
  display.drawRect(x, y, cellSize, cellSize, SSD1306_BLACK);
}

void newPiece() {
  currentX = gridWidth / 2 - 2;
  currentY = 0;
  currentShape = nextShape;
  currentRotation = nextRotation;
  nextShape = random(7);
  nextRotation = 0;
}

void lockPiece() {
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][currentRotation][y][x]) {
        if(currentY + y >= 0 && currentY + y < gridHeight && 
           currentX + x >= 0 && currentX + x < gridWidth) {
          tetrisGrid[currentY + y][currentX + x] = currentShape + 1;
        }
      }
    }
  }
}

bool validMove(int newX, int newY, int rotation) {
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][rotation][y][x]) {
        int gridX = newX + x;
        int gridY = newY + y;
        
        if (gridX < 0 || gridX >= gridWidth || gridY >= gridHeight) {
          return false;
        }
        
        if (gridY >= 0 && tetrisGrid[gridY][gridX] != 0) {
          return false;
        }
      }
    }
  }
  return true;
}

void clearLines() {
  int lines = 0;
  for (int y = gridHeight - 1; y >= 0; y--) {
    bool lineComplete = true;
    for (int x = 0; x < gridWidth; x++) {
      if (tetrisGrid[y][x] == 0) {
        lineComplete = false;
        break;
      }
    }
    
    if (lineComplete) {
      for (int yy = y; yy > 0; yy--) {
        for (int x = 0; x < gridWidth; x++) {
          tetrisGrid[yy][x] = tetrisGrid[yy-1][x];
        }
      }
      for (int x = 0; x < gridWidth; x++) {
        tetrisGrid[0][x] = 0;
      }
      lines++;
      y++;
    }
  }
  
  if (lines > 0) {
    int points = 0;
    switch (lines) {
      case 1: points = 40 * level; break;
      case 2: points = 100 * level; break;
      case 3: points = 300 * level; break;
      case 4: points = 1200 * level; break;
    }
    tetrisScore += points;
    linesCleared += lines;
    level = linesCleared / 10 + 1;
  }
}

void resetTetris() {
  tetrisActive = true;
  tetrisScore = 0;
  level = 1;
  linesCleared = 0;
  
  for (int y = 0; y < gridHeight; y++) {
    for (int x = 0; x < gridWidth; x++) {
      tetrisGrid[y][x] = 0;
    }
  }
  
  newPiece();
  nextShape = random(7);
  
  tetrisFrameCount = 0;
  lastTetrisFPSTime = millis();
  tetrisFPS = 0;
}

void tetrisGameOver() {
  tetrisActive = false;
  
  for (int i = 0; i < 3; i++) {
    display.invertDisplay(true);
    delay(200);
    display.invertDisplay(false);
    delay(200);
  }
  
  display.clearDisplay();
  display.setTextSize(1);
  display.setCursor(25, 20);
  display.print("GAME OVER");
  display.setCursor(15, 35);
  display.print("Score: ");
  display.print(tetrisScore);
  display.setCursor(20, 50);
  display.print("UP:Restart D:Menu");
  display.display();
}

void renderTetris() {
  display.clearDisplay();
  
  // 绘制游戏区域
  display.fillRect(gridOffsetX-1, gridOffsetY, 
                  gridWidth*cellSize+2, gridHeight*cellSize, SSD1306_BLACK);
  display.drawRect(gridOffsetX-1, gridOffsetY, 
                  gridWidth*cellSize+2, gridHeight*cellSize, SSD1306_WHITE);
  
  // 绘制网格方块
  for (int y = 0; y < gridHeight; y++) {
    for (int x = 0; x < gridWidth; x++) {
      if (tetrisGrid[y][x] != 0) {
        int colorIndex = tetrisGrid[y][x] - 1;
        drawSquareBlock(gridOffsetX + x*cellSize, 
                        gridOffsetY + y*cellSize,
                        tetrisColors[colorIndex]);
      }
    }
  }
  
  // 绘制当前方块
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][currentRotation][y][x]) {
        if (currentY + y >= 0) {
          drawSquareBlock(gridOffsetX + (currentX + x)*cellSize, 
                          gridOffsetY + (currentY + y)*cellSize,
                          tetrisColors[currentShape]);
        }
      }
    }
  }
  
  // 绘制信息面板
  display.drawRect(0, 0, 78, 64, SSD1306_WHITE);
  
  // 显示信息
  display.setCursor(5, 5);
  display.print("SCORE:");
  display.setCursor(5, 15);
  display.print(tetrisScore);
  
  display.setCursor(5, 25);
  display.print("LEVEL:");
  display.setCursor(5, 35);
  display.print(level);
  
  display.setCursor(5, 45);
  display.print("LINES:");
  display.setCursor(5, 55);
  display.print(linesCleared);
  
  // 显示下一个方块
  display.setCursor(45, 5);
  display.print("NEXT:");
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[nextShape][0][y][x]) {
        drawSquareBlock(45 + x*cellSize, 
                        15 + y*cellSize,
                        tetrisColors[nextShape]);
      }
    }
  }
  display.display();
}

void tetrisLoop() {
  if (!tetrisActive) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      resetTetris();
    }
    return;
  }
  
  // 帧率计算
  tetrisFrameCount++;
  if (millis() - lastTetrisFPSTime >= 1000) {
    tetrisFPS = tetrisFrameCount;
    tetrisFrameCount = 0;
    lastTetrisFPSTime = millis();
  }
  
  // 处理输入
  if (digitalRead(leftButton) == LOW) {
    if (validMove(currentX - 1, currentY, currentRotation)) {
      currentX--;
    }
    delay(150);
  }
  
  if (digitalRead(rightButton) == LOW) {
    if (validMove(currentX + 1, currentY, currentRotation)) {
      currentX++;
    }
    delay(150);
  }
  
  if (digitalRead(rotateButton) == LOW) {
    int newRotation = (currentRotation + 1) % 4;
    if (validMove(currentX, currentY, newRotation)) {
      currentRotation = newRotation;
    }
    delay(150);
  }
  
  if (digitalRead(downButton) == LOW) {
    fallSpeed = 100;
  } else {
    fallSpeed = 500 - (level - 1) * 50;
    if (fallSpeed < 100) fallSpeed = 100;
  }
  
  // 返回菜单检测
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  
  // 自动下落
  if (millis() - lastFall > fallSpeed) {
    if (validMove(currentX, currentY + 1, currentRotation)) {
      currentY++;
    } else {
      lockPiece();
      clearLines();
      newPiece();
      if (!validMove(currentX, currentY, currentRotation)) {
        tetrisGameOver();
      }
    }
    lastFall = millis();
  }
  
  // 渲染游戏
  renderTetris();
}

// ====================== 弹球游戏部分 ======================
// 游戏参数
const int paddleWidth = 20;
const int paddleHeight = 4;
int paddleX = (SCREEN_WIDTH - paddleWidth) / 2;
const int paddleY = SCREEN_HEIGHT - paddleHeight - 2;

const int ballSize = 4;
int ballX = SCREEN_WIDTH / 2;
int ballY = SCREEN_HEIGHT / 2;
float ballSpeedX = 1.8;  
float ballSpeedY = 1.8;  

int ballScore = 0;
bool canScore = true;
const unsigned long scoreCooldown = 200; 
unsigned long lastScoreTime = 0;  
unsigned long gameStartTime = 0;
float gameTime = 0.0;
unsigned long lastBallFPSTime = 0;
unsigned long ballFrameCount = 0;
float ballFPS = 0.0;
unsigned long lastLeftButtonTime = 0;
unsigned long lastRightButtonTime = 0;
unsigned long lastPausePressTime = 0;

// 暂停闪烁相关变量
bool pausedBlinkState = true;      // 当前暂停文字是否显示
int blinkCount = 0;                // 已完成的闪烁次数
unsigned long lastBlinkTime = 0;   // 上次闪烁时间
const int blinkInterval = 100;     // 闪烁间隔（毫秒）

// 弹球辅助函数
String formatTime(float totalSeconds) {
  int minutes = static_cast<int>(totalSeconds) / 60;
  int seconds = static_cast<int>(totalSeconds) % 60;
  int tenths = static_cast<int>((totalSeconds - static_cast<int>(totalSeconds)) * 10);
  
  char buffer[10];
  sprintf(buffer, "%d:%02d.%d", minutes, seconds, tenths);
  return String(buffer);
}

void resetBallGame() {
  paddleX = (SCREEN_WIDTH - paddleWidth) / 2;
  // 小球初始位置调整到顶部（球心位于顶部边缘，`ballSize/2` 确保球完全在屏幕内）
  ballX = SCREEN_WIDTH / 2;
  ballY = ballSize / 2; // 顶部起始位置（Y轴0为屏幕顶部，向下为正方向）

  // 固定初始速度方向：X轴随机，Y轴固定向下（正值）
  float baseSpeed = 1.8f * difficultyFactors[difficultySelection];
  ballSpeedX = (random(0, 2) == 1) ? baseSpeed : -baseSpeed; // X轴随机左右
  ballSpeedY = -baseSpeed; // Y轴固定向下（正值，根据坐标系方向）

  ballScore = 0;
  ballState = PLAYING;
  ballFrameCount = 0;
  gameStartTime = millis();
  gameTime = 0.0;
  canScore = true;

  // 重置暂停闪烁状态
  pausedBlinkState = true;
  blinkCount = 0;
  lastBlinkTime = 0;
}

void processBallInput() {
  unsigned long currentTime = millis();
  
  // 左移动
  if(digitalRead(leftButton) == LOW) {
    if(currentTime - lastLeftButtonTime > 10) {
      paddleX -= 4;
      if(paddleX < 0) paddleX = 0;
      lastLeftButtonTime = currentTime;
    }
  }
  
  // 右移动
  if(digitalRead(rightButton) == LOW) {
    if(currentTime - lastRightButtonTime > 10) {
      paddleX += 4;
      if(paddleX > SCREEN_WIDTH - paddleWidth) paddleX = SCREEN_WIDTH - paddleWidth;
      lastRightButtonTime = currentTime;
    }
  }
  
  // 暂停/继续功能
  if(digitalRead(rotateButton) == LOW) {
    if (ballState != GAME_OVER && (currentTime - lastPausePressTime) > 250) {
      if (ballState == PAUSED) {
        ballState = PLAYING;
        gameStartTime += (currentTime - gameStartTime - gameTime*1000);
      } else if (ballState == PLAYING) {
        ballState = PAUSED;
        // 进入暂停状态时重置闪烁变量
        pausedBlinkState = true;
        blinkCount = 0;
        lastBlinkTime = currentTime;
      }
      lastPausePressTime = currentTime;
    }
  }
}

void updateBallGame() {
  if (ballState == PLAYING) {
    ballX += ballSpeedX;
    ballY += ballSpeedY;
    
    if(ballX <= 0) {
      ballX = 0;
      ballSpeedX = abs(ballSpeedX);
    }
    if(ballX + ballSize >= SCREEN_WIDTH) {
      ballX = SCREEN_WIDTH - ballSize;
      ballSpeedX = -abs(ballSpeedX);
    }
    if(ballY <= 0) {
      ballY = 0;
      ballSpeedY = abs(ballSpeedY);
    }
    
    // 球与板碰撞检测
    if(ballY + ballSize >= paddleY && 
       ballY <= paddleY + paddleHeight && 
       ballX + ballSize >= paddleX && 
       ballX <= paddleX + paddleWidth &&
       canScore) {
      
      ballY = paddleY - ballSize;
      int hitPos = (ballX + ballSize/2) - (paddleX + paddleWidth/2);
      ballSpeedX = constrain(hitPos / 4, -5, 5);
      ballSpeedY = -abs(ballSpeedY);
      
      // 得分
      ballScore++;
      canScore = false;
      lastScoreTime = millis();
      
      // 根据难度增加速度
      float speedIncrease = 0.1f * difficultyFactors[difficultySelection];
      if(abs(ballSpeedX) < 6) {
        ballSpeedX += (ballSpeedX > 0) ? speedIncrease : -speedIncrease;
      }
    }
    
    // 球掉落到底部 - 游戏结束
    if(ballY >= SCREEN_HEIGHT) {
      ballState = GAME_OVER;
    }
  }
}

void renderBallGame() {
  display.clearDisplay();
  
  // 绘制挡板
  if (ballState == PLAYING) {
    display.fillRect(paddleX, paddleY, paddleWidth, paddleHeight, SSD1306_WHITE);
  }
  
  // 绘制球
  display.fillCircle(ballX + ballSize/2, ballY + ballSize/2, ballSize/2, SSD1306_WHITE);
  
  // 显示分数和帧数
  display.setCursor(0, 0);
  display.print("S:");
  display.print(ballScore);
  
  display.setCursor(SCREEN_WIDTH - 30, 0);
  display.print(static_cast<int>(ballFPS));
  display.print("fps");
  
  // 显示游戏时间
  if (ballState != GAME_OVER) {
    String timeString = formatTime(gameTime);
    display.setCursor(SCREEN_WIDTH/2 - 18, 0);
    display.print(timeString);
    
    // 只在暂停状态显示难度 - 游戏进行中隐藏
    if (ballState == PAUSED) {
      display.setCursor(SCREEN_WIDTH/2 - 18, SCREEN_HEIGHT - 8);
      display.print(difficultyNames[difficultySelection]);
    }
  }
  
  // 游戏暂停时显示暂停文本（带闪烁效果）
  if (ballState == PAUSED) {
    // 处理闪烁逻辑
    if (blinkCount < 6) { // 总共闪烁3次（每次包括显示和隐藏）
      if (millis() - lastBlinkTime >= blinkInterval) {
        pausedBlinkState = !pausedBlinkState;
        lastBlinkTime = millis();
        blinkCount++;
      }
    } else {
      // 闪烁3次后保持显示
      pausedBlinkState = true;
    }
    
    // 根据闪烁状态决定是否绘制暂停文字
    if (pausedBlinkState) {
      display.setTextSize(2);
      display.setCursor(SCREEN_WIDTH/2 - 35, SCREEN_HEIGHT/2 - 10);
      display.print("PAUSED");
      display.setTextSize(1);
    }
  }
  
  display.display();
}

void ballGameOverScreen() {
  display.clearDisplay();
  
  // 游戏结束文字动画
  static int textOffset = 0;
  static bool textDirection = true;
  
  if (textDirection) {
    textOffset += 2;
    if (textOffset > 8) textDirection = false;
  } else {
    textOffset -= 2;
    if (textOffset < -8) textDirection = true;
  }
  
  display.setCursor(33, 10 + textOffset);
  display.println("Game Over!");
  display.setCursor(40, 25);
  display.print("Score: ");
  display.print(ballScore);
  
  // 显示时间和难度
  String timeString = formatTime(gameTime);
  display.setCursor(29, 35);
  display.print("Time: ");
  display.print(timeString);
  
  // 在游戏结束界面显示难度
  display.setCursor(29, 45);
  display.print("Level: ");
  display.print(difficultyNames[difficultySelection]);
  
  // 操作提示
  display.setCursor(3, SCREEN_HEIGHT-7);
  display.print("UP:Restart DOWN:Menu");
  
  display.display();

  
  // 处理输入
  if (digitalRead(rotateButton) == LOW) {
    resetBallGame();
    delay(150);
  }
  
  if (digitalRead(downButton) == LOW) {
    currentGame = MENU;
    delay(150);
  }
}

void ballGameLoop() {
  unsigned long currentTime = millis();
  
  // FPS计算
  ballFrameCount++;
  if (currentTime - lastBallFPSTime >= 1000) {
    ballFPS = ballFrameCount;
    ballFrameCount = 0;
    lastBallFPSTime = currentTime;
  }
  
  // 更新游戏时间
  if (ballState == PLAYING && gameStartTime != 0) {
    gameTime = (currentTime - gameStartTime) / 1000.0;
  }
  
  // 检查得分冷却期
  if (!canScore && (currentTime - lastScoreTime > scoreCooldown)) {
    canScore = true;
  }
  
  // 返回菜单检测
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  
  if (ballState == GAME_OVER) {
    ballGameOverScreen();
    return;
  }
  
  processBallInput();
  
  // 只在游戏进行中更新状态
  if (ballState != PAUSED) {
    updateBallGame();
  }
  
  renderBallGame();
}

// ====================== 立方体游戏部分 ======================
// 立方体定义
const int CUBE_SIZE = 20;
const int CUBE_VERTICES = 8;
int cube3D[CUBE_VERTICES][3] = {
  {-CUBE_SIZE, -CUBE_SIZE, -CUBE_SIZE},
  { CUBE_SIZE, -CUBE_SIZE, -CUBE_SIZE},
  { CUBE_SIZE,  CUBE_SIZE, -CUBE_SIZE},
  {-CUBE_SIZE,  CUBE_SIZE, -CUBE_SIZE},
  {-CUBE_SIZE, -CUBE_SIZE,  CUBE_SIZE},
  { CUBE_SIZE, -CUBE_SIZE,  CUBE_SIZE},
  { CUBE_SIZE,  CUBE_SIZE,  CUBE_SIZE},
  {-CUBE_SIZE,  CUBE_SIZE,  CUBE_SIZE}
};

const int CUBE_EDGES = 12;
int edges[CUBE_EDGES][2] = {
  {0, 1}, {1, 2}, {2, 3}, {3, 0}, // 底面
  {4, 5}, {5, 6}, {6, 7}, {7, 4}, // 顶面
  {0, 4}, {1, 5}, {2, 6}, {3, 7}  // 侧面
};

// 投影后的2D点
int cube2D[CUBE_VERTICES][2];

// 投影参数
const float PROJ_DISTANCE = 200.0;
const float PROJ_SCALE = 100.0;
unsigned long lastCubeFPSTime = 0;
int cubeFrameCount = 0;
int cubeFPS = 0;

// 立方体辅助函数
void projectPoint(float x, float y, float z, int &screenX, int &screenY) {
  float factor = PROJ_SCALE / (PROJ_DISTANCE - z);
  screenX = (int)(x * factor) + SCREEN_WIDTH/2;
  screenY = (int)(y * factor) + SCREEN_HEIGHT/2;
}

void rotateX(float &y, float &z, float angle) {
  float rad = angle * PI / 180.0;
  float yTemp = y;
  y = y * cos(rad) - z * sin(rad);
  z = yTemp * sin(rad) + z * cos(rad);
}

void rotateY(float &x, float &z, float angle) {
  float rad = angle * PI / 180.0;
  float xTemp = x;
  x = x * cos(rad) + z * sin(rad);
  z = -xTemp * sin(rad) + z * cos(rad);
}

void rotateZ(float &x, float &y, float angle) {
  float rad = angle * PI / 180.0;
  float xTemp = x;
  x = x * cos(rad) - y * sin(rad);
  y = xTemp * sin(rad) + y * cos(rad);
}

void updateCube() {
  angleY += 2.0;
  angleX += 1.8;
  
  if (digitalRead(leftButton) == LOW) {
    angleY -= 10;
    delay(10);
  }
  if (digitalRead(rightButton) == LOW) {
    angleY += 10;
    delay(10);
  }
  if (digitalRead(downButton) == LOW) {
    angleX -= 10;
    delay(10);
  }
  if (digitalRead(rotateButton) == LOW) {
    angleZ += 10;
    delay(10);
  }
  
  for (int i = 0; i < CUBE_VERTICES; i++) {
    float x = cube3D[i][0];
    float y = cube3D[i][1];
    float z = cube3D[i][2];
    
    rotateX(y, z, angleX);
    rotateY(x, z, angleY);
    rotateZ(x, y, angleZ);
    
    projectPoint(x, y, z, cube2D[i][0], cube2D[i][1]);
  }
}

void renderCube() {
  display.clearDisplay();
  
  for (int i = 0; i < CUBE_EDGES; i++) {
    int startIdx = edges[i][0];
    int endIdx = edges[i][1];
    
    display.drawLine(
      cube2D[startIdx][0], cube2D[startIdx][1],
      cube2D[endIdx][0], cube2D[endIdx][1],
      SSD1306_WHITE
    );
  }
  
  cubeFrameCount++;
  if (millis() - lastCubeFPSTime >= 1000) {
    cubeFPS = cubeFrameCount;
    cubeFrameCount = 0;
    lastCubeFPSTime = millis();
  }
  
  display.setCursor(SCREEN_WIDTH - 30, 0);
  display.print(cubeFPS);
  display.print("fps");
  
  display.setCursor(0, SCREEN_HEIGHT - 8);
  display.print("BY canhui  Test 3D");
  
  display.display();
}

void cubeLoop() {
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  
  updateCube();
  renderCube();
}

// ====================== 计算器功能 ======================
void calcDrawCalculator() {
  display.clearDisplay();
  
  // 显示输入区域 - 顶部14像素高度
  display.fillRect(0, 0, SCREEN_WIDTH, 14, SSD1306_WHITE);
  display.setTextColor(SSD1306_BLACK);
  
  // 显示输入内容
  String displayText = calcInput.length() > 0 ? calcInput : "0";
  
  // 根据文本长度调整显示
  if (displayText.length() > 16) {
    // 截断过长的文本
    displayText = "..." + displayText.substring(displayText.length() - 13);
  }
  // 垂直居中显示
  display.setCursor(2, (14 - 8) / 2);
  display.print(displayText);
  
  display.setTextColor(SSD1306_WHITE);
  
  // 绘制4行虚拟键盘（无边框）
  for (uint8_t row = 0; row < 4; row++) {
    for (uint8_t col = 0; col < 6; col++) {
      uint8_t index = row * 6 + col;
      
      uint8_t x = startX + col * (btnWidth + btnPadding);
      uint8_t y = startY + row * (btnHeight + btnPadding);
      
      // 检查是否在屏幕范围内
      if (y + btnHeight > SCREEN_HEIGHT) continue;
      
      // 居中文本
      const char* btnText = buttons[index];
      uint8_t textWidth = strlen(btnText) * 6;
      uint8_t textX = x + (btnWidth - textWidth) / 2;
      uint8_t textY = y + (btnHeight - 8) / 2;
      
      // 只高亮显示选中的按钮
      if (index == selectedButton) {
        // 绘制选中的按钮背景
        display.fillRect(x, y, btnWidth, btnHeight, SSD1306_WHITE);
        display.setTextColor(SSD1306_BLACK);
        display.setCursor(textX, textY);
        display.print(btnText);
        display.setTextColor(SSD1306_WHITE);
      } else {
        // 非选中按钮只显示文本
        display.setCursor(textX, textY);
        display.print(btnText);
      }
    }
  }
  
  display.display();
}

// 格式化数字显示（移除多余的0）
String formatNumber(float num) {
  String result = String(num);
  
  // 移除小数点后多余的零
  if (result.indexOf('.') != -1) {
    // 删除末尾的零
    while (result.endsWith("0")) {
      result.remove(result.length() - 1);
    }
    // 如果以小数点结尾，删除小数点
    if (result.endsWith(".")) {
      result.remove(result.length() - 1);
    }
  }
  
  // 处理科学计数法显示
  if (result.length() > 10 && result.indexOf('e') == -1) {
    result = String(num, 6); // 限制小数位数
    // 再次移除多余的零
    if (result.indexOf('.') != -1) {
      while (result.endsWith("0")) {
        result.remove(result.length() - 1);
      }
      if (result.endsWith(".")) result.remove(result.length() - 1);
    }
  }
  
  return result;
}

void calcHandleButtonPress(uint8_t btnIndex) {
  if (btnIndex < 0 || btnIndex > 23) return;
  
  String btnValue = buttons[btnIndex];
  float currentValue = calcInput.length() > 0 ? calcInput.toFloat() : 0;
  
  // 处理数字输入
  if (btnValue.toInt() != 0 || btnValue == "0") {
    if (newInput) {
      calcInput = "";
      newInput = false;
      operationSet = false;
    }
    calcInput += btnValue;
  }
  // 处理小数点
  else if (btnValue == ".") {
    if (newInput) {
      calcInput = "0.";
      newInput = false;
    } else if (calcInput.indexOf('.') == -1) {
      calcInput += ".";
    }
  }
  // 处理清除
  else if (btnValue == "C") {
    calcInput = "";
    operand1 = 0;
    operation = '\0';
    newInput = true;
    operationSet = false;
  }
  // 处理删除
  else if (btnValue == "DEL") {
    if (calcInput.length() > 0) {
      calcInput.remove(calcInput.length() - 1);
    }
  }
  // 处理π
  else if (btnValue == "π") {
    calcInput = "3.141592";
    newInput = true;
  }
  // 处理基本运算符
  else if (btnValue == "+" || btnValue == "-" || btnValue == "*" || btnValue == "/") {
    if (calcInput.length() > 0) {
      operand1 = calcInput.toFloat();
      operation = btnValue.charAt(0);
      newInput = true;
      operationSet = true;
    }
  }
  // 处理等于
  else if (btnValue == "=") {
    if (operation != '\0' && calcInput.length() > 0) {
      float operand2 = calcInput.toFloat();
      float result = 0;
      
      switch (operation) {
        case '+': 
          result = operand1 + operand2; 
          break;
        case '-': 
          result = operand1 - operand2; 
          break;
        case '*': 
          result = operand1 * operand2; 
          break;
        case '/': 
          if (operand2 != 0) result = operand1 / operand2;
          else result = 0; // 避免除以零
          break;
      }
      
      calcInput = formatNumber(result);
      operation = '\0';
      newInput = true;
      operationSet = false;
    }
  }
  // 处理高级运算 (单目运算符)
  else if (calcInput.length() > 0) {
    float result = 0;
    
    if (btnValue == "√") {  // 平方根
      if (currentValue >= 0) {
        result = sqrt(currentValue);
        calcInput = formatNumber(result);
      }
    } 
    else if (btnValue == "%") {  // 百分号
      result = currentValue / 100.0;
      calcInput = formatNumber(result);
    }
    else if (btnValue == "+/-") {  // 正负号切换
      result = -currentValue;
      calcInput = formatNumber(result);
    }
    
    // 高级运算后重置状态
    if (btnValue != "+/-") {
      newInput = true;
    }
  }
  
  // 处理括号（暂时只作为字符输入）
  else if (btnValue == "(" || btnValue == ")") {
    if (newInput) {
      calcInput = "";
      newInput = false;
    }
    calcInput += btnValue;
  }
  
  calcDrawCalculator();
}

void calculatorLoop() {
  // 读取按键状态
  bool select1Reading = digitalRead(rotateButton);   // 左移
  bool select2Reading = digitalRead(downButton);     // 右移
  bool downReading = digitalRead(leftButton);        // 下移
  bool confirmReading = digitalRead(rightButton);    // 确认
  
  // 按键消抖
  unsigned long currentTime = millis();
  if (currentTime - lastDebounceTime > debounceDelay) {
    
    // 选择按钮1 - 向左移动
    if (select1Reading == LOW && lastSelect1State == HIGH) {
      selectedButton--;
      if (selectedButton < 0) selectedButton = 23;
      calcDrawCalculator();
    }
    
    // 选择按钮2 - 向右移动
    if (select2Reading == LOW && lastSelect2State == HIGH) {
      selectedButton++;
      if (selectedButton > 23) selectedButton = 0;
      calcDrawCalculator();
    }
    
    // 向下按钮 - 向下移动
    if (downReading == LOW && lastDownState == HIGH) {
      selectedButton += 6; // 移动到下一行
      if (selectedButton > 23) {
        // 如果超出范围，循环到第一行
        selectedButton %= 6; // 回到第一行相同列
      }
      calcDrawCalculator();
    }
    
    // 确认按钮 - 执行操作
    if (confirmReading == LOW && lastConfirmState == HIGH) {
      calcHandleButtonPress(selectedButton);
    }
    
    // 更新按键状态
    lastSelect1State = select1Reading;
    lastSelect2State = select2Reading;
    lastDownState = downReading;
    lastConfirmState = confirmReading;
    
    lastDebounceTime = currentTime;
  }
  
  // 返回菜单检测
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

// ====================== INFO 功能 (新增) ======================
void infoLoop() {
  display.clearDisplay();
  
  // 显示标题
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(45, 0);
  display.print("INFO");
  
  // 显示分隔线
  display.drawLine(0, 9, SCREEN_WIDTH, 9, SSD1306_WHITE);
  
  // 显示信息内容
  display.setTextSize(1);
  display.setCursor(1, 11);
  display.print("Canhui's production");
  display.setCursor(1, 24);
  display.print("AI-Thinker BW16");
  display.setCursor(1, 32);
  display.print("RTL8720DN 200MHZ");
  display.setCursor(1, 40);
  display.print("canhui UI V1.0");
  display.setCursor(1, 48);
  display.print("QQ:2313518480");
  display.setCursor(1, 56);
  display.print("WeChat:dsdsddss123");

  
  display.display();
  
  // 检测任意按钮按下以返回菜单
  if (digitalRead(leftButton) == LOW || 
      digitalRead(rightButton) == LOW || 
      digitalRead(rotateButton) == LOW || 
      digitalRead(downButton) == LOW) {
    currentGame = MENU;
    delay(200);
  }
}

// ====================== 主函数 ======================
void setup() {
  Serial.begin(9600);
  
  // 初始化OLED
  if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    Serial.println(F("SSD1306 allocation failed"));
    for(;;);
  }
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // 初始化按钮
  pinMode(leftButton, INPUT_PULLUP);
  pinMode(rightButton, INPUT_PULLUP);
  pinMode(rotateButton, INPUT_PULLUP);
  pinMode(downButton, INPUT_PULLUP);
  
  // 显示欢迎信息
  display.clearDisplay();
  display.setTextSize(1);
  display.setCursor(37, 4);
  display.print("BETA TEST");
  display.setCursor(37, 13);
  display.print("BY canhui");
  display.setCursor(17, 23);
  display.print("MEMU Game System");
  display.setCursor(35, 43);
  display.print("Starting...");
  display.display();
  
  //自动进入菜单
  delay(1200);
  currentGame = MENU;
}

void loop() {
  switch(currentGame) {
    case MENU:
      menuLoop();
      break;
    case TETRIS:
      tetrisLoop();
      break;
    case BALL_GAME:
      ballGameLoop();
      break;
    case CUBE_GAME:
      cubeLoop();
      break;
    case BALL_MENU:  // 处理弹球游戏难度选择
      ballMenuLoop();
      break;
    case TIMER_GAME: // 处理计时器功能
      timerLoop();
      break;
    case CALCULATOR: // 处理计算器功能
      calculatorLoop();
      break;
    case INFO:       // 新增INFO状态处理
      infoLoop();
      break;
  }
}