#ifndef PROTECT_H
#define PROTECT_H

#include <WiFi.h>
#include <FlashMemory.h>
#include "mbedtls/md5.h"
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <U8g2_for_Adafruit_GFX.h>
#include <WiFiServer.h>
#include <WiFiClient.h>

// Add forward declarations for display objects
class Adafruit_SSD1306;
class U8G2_FOR_ADAFRUIT_GFX;
extern Adafruit_SSD1306 display;
extern U8G2_FOR_ADAFRUIT_GFX u8g2Font;

#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif

#define HASH_ADDRESS 0x00100000  // Flash基地址
#define HASH_OFFSET  0x0000      // Flash内的偏移量
#define HASH_LENGTH  32          // MD5哈希值的十六进制字符串长度
#define MAC_OFFSET   0x0020      // MAC地址在Flash中的偏移量
#define MAC_LENGTH   12          // MAC地址的十六进制字符串长度

// 安全增强常量
#define MAX_ATTEMPTS 5           // 最大尝试次数
#define ATTEMPT_WINDOW 300       // 5分钟窗口（秒）
#define ACTIVATION_TIMEOUT 120   // 激活码有效期（秒）
#define MIN_RETRY_DELAY 10       // 最小重试间隔（秒）

const String SALT = "SALTYFISH";
char* ACTIVATION_AP_SSID = "bw16激活";
char* ACTIVATION_AP_PASS = "88888888";
const int ACTIVATION_PORT = 80;

String globalMACAddress = "";
String currentCaptcha = "";      // 当前验证码
WiFiServer activationServer(ACTIVATION_PORT);

// 安全增强变量
unsigned long activationStartTime = 0;  // 激活开始时间（秒）
int activationAttempts = 0;             // 当前尝试次数
unsigned long lastActivationAttempt = 0; // 上次尝试时间（秒）

String readStringFromFlash(uint32_t address, size_t length);
void writeStringToFlash(uint32_t address, const String& str, size_t length);

// 从Flash读取MAC
String readMACFromFlash() {
  return readStringFromFlash(MAC_OFFSET, MAC_LENGTH);
}

// 将MAC写入Flash
void writeMACToFlash(String mac) {
  Serial.print("正在写入MAC地址到Flash: ");
  Serial.println(mac);
  writeStringToFlash(MAC_OFFSET, mac, MAC_LENGTH);
  Serial.println("MAC地址已成功写入Flash存储器");
}

// 将二进制MD5转换为十六进制字符串
String toHexString(const unsigned char *hash, size_t length = 16) {
  char hex[33];
  for (size_t i = 0; i < length; i++) {
    sprintf(&hex[i * 2], "%02x", hash[i]);
  }
  hex[32] = '\0';
  return String(hex);
}

// 使用mbedTLS执行10次MD5哈希
String multipleMD5(String data) {
  unsigned char hash[16];
  String currentData = data;

  // 执行10次MD5哈希
  for (int i = 0; i < 10; i++) {
    mbedtls_md5((const unsigned char*)currentData.c_str(), currentData.length(), hash);
    currentData = toHexString(hash);
  }

  return currentData;
}

//获取持久化的MAC地址作为不带分隔符的大写字符串
String getMACAddress() {
  //如果已经加载过MAC地址，直接返回
  if (globalMACAddress.length() == 12) {
    return globalMACAddress;
  }

  // 首先尝试从Flash读取已保存的MAC地址
  String storedMAC = readMACFromFlash();

  bool isValidMAC = false;
  if (storedMAC.length() == 12) {
    //检查字符格式
    bool formatValid = true;
    for (int i = 0; i < 12; i++) {
      char c = storedMAC.charAt(i);
      if (!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F'))) {
        formatValid = false;
        break;
      }
    }

    if (formatValid) {
      isValidMAC = true;
    }
  }

  if (isValidMAC) {
    globalMACAddress = storedMAC;
    return storedMAC;
  }

  Serial.println("Flash中无有效MAC地址，正在生成新的随机MAC地址...");

  //随机种子生成算法
  unsigned long finalSeed = 0;

  for (int i = 0; i < 20; i++) {
    unsigned long entropy = micros();
    finalSeed ^= entropy;
    finalSeed = (finalSeed << 1) | (finalSeed >> 31); // 循环左移

    delayMicroseconds(50 + (i * 7) % 100);
  }

  finalSeed ^= millis();

  finalSeed ^= (unsigned long)&finalSeed; // 栈地址

  finalSeed ^= (finalSeed << 13);
  finalSeed ^= (finalSeed >> 17);
  finalSeed ^= (finalSeed << 5);
  finalSeed ^= (finalSeed >> 11);
  finalSeed ^= (finalSeed << 7);
  finalSeed ^= (finalSeed >> 19);

  if (finalSeed == 0 || finalSeed == 0xFFFFFFFF) {
    finalSeed = 0x9E3779B9;
  }

  randomSeed(finalSeed);

  //生成随机MAC地址
  String macStr = "";
  uint8_t macBytes[6];

  int attempts = 0;
  bool validMAC = false;

  while (!validMAC && attempts < 10) {
    attempts++;

    macBytes[0] = 0x02 | ((random(0, 256) & 0xFC));

    for (int i = 1; i < 6; i++) {
      macBytes[i] = random(0, 256);
      delayMicroseconds(10 + (i * 3));
      macBytes[i] ^= (micros() & 0xFF);
      macBytes[i] ^= (i * 0x5A);
      macBytes[i] = (macBytes[i] << 3) | (macBytes[i] >> 5);
    }

    validMAC = true;

    for (int i = 0; i < 6; i++) {
      int sameCount = 0;
      for (int j = 0; j < 6; j++) {
        if (macBytes[i] == macBytes[j]) sameCount++;
      }
      if (sameCount > 3) { 
        validMAC = false;
        break;
      }
    }

    if (validMAC) {
      bool isPattern = true;
      for (int i = 1; i < 6; i++) {
        if (macBytes[i] != macBytes[0]) {
          isPattern = false;
          break;
        }
      }
      if (isPattern) validMAC = false;
    }
  }

  //转换为十六进制字符串
  for (int i = 0; i < 6; i++) {
    if (macBytes[i] < 16) macStr += "0";
    macStr += String(macBytes[i], HEX);
  }

  macStr.toUpperCase();

  //验证最终生成的MAC地址字符串
  if (macStr.length() != 12) {
    macStr = "02" + String(millis(), HEX) + String(micros(), HEX);
    macStr = macStr.substring(0, 12);
    macStr.toUpperCase();
  }

  //存储到全局变量和Flash
  globalMACAddress = macStr;
  writeMACToFlash(macStr);

  return macStr;
}

//从Flash读取字符串
String readStringFromFlash(uint32_t address, size_t length) {
  char buffer[length + 1];
  for (size_t i = 0; i < length; i += 4) {
    uint32_t word = FlashMemory.readWord(address + i);
    memcpy(buffer + i, &word, min((size_t)4, length - i));
  }
  buffer[length] = '\0';
  return String(buffer);
}

//向Flash写入字符串
void writeStringToFlash(uint32_t address, const String& str, size_t length) {
  char buffer[length + 1];
  str.toCharArray(buffer, length + 1);
  for (size_t i = 0; i < length; i += 4) {
    uint32_t word = 0;
    memcpy(&word, buffer + i, min((size_t)4, length - i));
    FlashMemory.writeWord(address + i, word);
  }
}

//从Flash读取哈希值
String readHashFromFlash() {
  return readStringFromFlash(HASH_OFFSET, HASH_LENGTH);
}

//将哈希值写入Flash
void writeHashToFlash(String hash) {
  writeStringToFlash(HASH_OFFSET, hash, HASH_LENGTH);
}

// 生成4位随机验证码
String generateCaptcha() {
  randomSeed(micros());
  String captcha = "";
  for (int i = 0; i < 4; i++) {
    captcha += (char)('0' + random(0, 10));
  }
  return captcha;
}

// 错误响应函数
void sendErrorResponse(WiFiClient& client, String message) {
  String response = "HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\n\r\n";
  response += "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>激活错误</title>";
  response += "<style>body{font-family:Arial,sans-serif;text-align:center;padding:20px;}";
  response += ".error{color:#e74c3c;font-weight:bold;margin:20px 0;}</style></head>";
  response += "<body><h1 style='color:red;'>激活失败</h1>";
  response += "<p class='error'>" + message + "</p>";
  response += "<p><a href='/'>返回激活页面</a></p></body></html>";
  client.print(response);
}

// 生成激活页面HTML（带验证码）
String generateActivationPage(String macAddress) {
  // 生成新的验证码
  currentCaptcha = generateCaptcha();
  
  String html = R"=====(
  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BW16 设备激活</title>
    <style>
      body { 
        font-family: Arial, sans-serif; 
        background-color: #f4f4f4;
        text-align: center;
        padding: 20px;
      }
      .container {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        max-width: 400px;
        margin: 0 auto;
      }
      h1 {
        color: #2c3e50;
      }
      .mac-address {
        font-size: 18px;
        font-weight: bold;
        color: #3498db;
        margin: 15px 0;
      }
      input[type="text"] {
        width: 100%;
        padding: 10px;
        margin: 10px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      input[type="submit"] {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 10px;
      }
      input[type="submit"]:hover {
        background-color: #2980b9;
      }
      .instructions {
        margin: 20px 0;
        text-align: left;
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
      }
      .error {
        color: #e74c3c;
        font-weight: bold;
        margin: 10px 0;
      }
      .captcha-container {
        display: flex;
        align-items: center;
        margin: 15px 0;
      }
      .captcha-code {
        font-weight: bold;
        font-size: 20px;
        letter-spacing: 3px;
        background: #f0f0f0;
        padding: 8px 15px;
        margin-right: 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
        min-width: 80px;
      }
      .refresh-btn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .refresh-btn:hover {
        background-color: #5a6268;
      }
    </style>
    <script>
      function refreshCaptcha() {
        // 添加时间戳防止缓存
        document.getElementById('captchaDisplay').innerText = '加载中...';
        fetch('/refresh-captcha?' + new Date().getTime())
          .then(response => response.text())
          .then(data => {
            document.getElementById('captchaDisplay').innerText = data;
          })
          .catch(error => {
            console.error('刷新验证码失败:', error);
            document.getElementById('captchaDisplay').innerText = '????';
          });
        return false;
      }
    </script>
  </head>
  <body>
    <div class="container">
      <h1>BW16 设备激活</h1>
      <p>设备MAC地址：</p>
      <p class="mac-address">)=====";
  html += macAddress;
  html += R"=====(</p>
      <form method="POST" action="/activate">
        <input type="text" name="code" placeholder="请输入激活码" required>
        <div class="captcha-container">
          <div class="captcha-code" id="captchaDisplay">)=====";
  html += currentCaptcha;
  html += R"=====(</div>
          <input type="text" name="captcha" placeholder="请输入验证码" required>
          <button class="refresh-btn" onclick="return refreshCaptcha()">刷新</button>
        </div>
        <input type="hidden" name="captcha_code" value=")=====";
  html += currentCaptcha;
  html += R"=====(">
        <br>
        <input type="submit" value="激活设备">
      </form>
      <div class="instructions">
        <h3>激活说明：</h3>
        <p>1. 联系QQ群管理员获取激活码</p>
        <p>2. 在输入框中填写激活码</p>
        <p>3. 输入右侧显示的验证码</p>
        <p>4. 点击"激活设备"按钮完成激活</p>
        <p>请找 天子 ghost 浪木 王灿辉获取激活码</p> 
        <p>QQ群号: 552674356</p>
      </div>
    </div>
  </body>
  </html>
  )=====";
  return html;
}

// 处理激活请求
void handleActivation(WiFiClient& client, String request, String macAddress) {
  unsigned long currentTime = millis() / 1000;
  
  // 检查尝试频率限制
  if (currentTime - lastActivationAttempt < MIN_RETRY_DELAY) {
    sendErrorResponse(client, "请求过于频繁，请10秒后再试");
    return;
  }
  
  // 检查尝试次数限制
  if (activationAttempts >= MAX_ATTEMPTS) {
    if (currentTime - activationStartTime < ATTEMPT_WINDOW) {
      sendErrorResponse(client, "尝试次数过多，请5分钟后再试");
      return;
    } else {
      // 重置计数器
      activationAttempts = 0;
      activationStartTime = currentTime;
    }
  }
  
  // 更新尝试记录
  lastActivationAttempt = currentTime;
  activationAttempts++;

  // 检查会话超时
  if (currentTime - activationStartTime > ACTIVATION_TIMEOUT) {
    sendErrorResponse(client, "激活会话已过期，请刷新页面");
    return;
  }

  // 获取Content-Length
  int contentLength = 0;
  int lenIndex = request.indexOf("Content-Length: ");
  if (lenIndex != -1) {
    int lenEnd = request.indexOf("\r\n", lenIndex);
    if (lenEnd != -1) {
      String lenStr = request.substring(lenIndex + 16, lenEnd);
      contentLength = lenStr.toInt();
    }
  }
  
  // 完整读取请求体
  String body = "";
  if (contentLength > 0) {
    int bodyStart = request.indexOf("\r\n\r\n") + 4;
    if (bodyStart > 0) {
      body = request.substring(bodyStart);
      // 继续读取剩余内容
      while (body.length() < contentLength && client.connected()) {
        if (client.available()) {
          body += (char)client.read();
        }
      }
      body = body.substring(0, contentLength);
    }
  }
  
  // 解析参数
  String activationCode = "";
  String captchaInput = "";
  String captchaCode = "";
  
  if (body.length() > 0) {
    // 解析激活码
    int codeStart = body.indexOf("code=");
    if (codeStart != -1) {
      activationCode = body.substring(codeStart + 5);
      // 截取到第一个&或结束
      int codeEnd = activationCode.indexOf('&');
      if (codeEnd != -1) {
        activationCode = activationCode.substring(0, codeEnd);
      }
      
      // URL解码
      String decoded = "";
      for (int i = 0; i < activationCode.length(); i++) {
        char c = activationCode[i];
        if (c == '+') {
          decoded += ' ';
        } else if (c == '%' && i + 2 < activationCode.length()) {
          char hex[3] = { activationCode[i+1], activationCode[i+2], '\0' };
          decoded += (char)strtol(hex, NULL, 16);
          i += 2;
        } else {
          decoded += c;
        }
      }
      activationCode = decoded;
    }
    
    // 解析验证码输入
    int captchaStart = body.indexOf("captcha=");
    if (captchaStart != -1) {
      captchaInput = body.substring(captchaStart + 8);
      int captchaEnd = captchaInput.indexOf('&');
      if (captchaEnd != -1) {
        captchaInput = captchaInput.substring(0, captchaEnd);
      }
    }
    
    // 解析服务器生成的验证码
    int captchaCodeStart = body.indexOf("captcha_code=");
    if (captchaCodeStart != -1) {
      captchaCode = body.substring(captchaCodeStart + 13);
      int captchaCodeEnd = captchaCode.indexOf('&');
      if (captchaCodeEnd != -1) {
        captchaCode = captchaCode.substring(0, captchaCodeEnd);
      }
    }
  }
  
  // 验证验证码
  if (captchaInput.length() == 0 || captchaInput != captchaCode) {
    sendErrorResponse(client, "验证码错误");
    return;
  }

  // 统一转换为小写比较
  activationCode.toLowerCase();
  
  // 验证激活码
  String expectedHash = multipleMD5(SALT + macAddress);
  String response;
  
  if (activationCode.equals(expectedHash)) {
    writeHashToFlash(activationCode);
    activationAttempts = 0; // 重置计数器
    
    // 成功响应
    response = "HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\n\r\n";
    response += "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>激活成功</title>";
    response += "<style>body{font-family:Arial,sans-serif;text-align:center;padding:20px;}";
    response += ".success{color:#27ae60;font-weight:bold;margin:20px 0;}</style></head>";
    response += "<body><h1 style='color:green;'>激活成功！</h1>";
    response += "<p class='success'>设备已成功激活，请重新启动设备。</p>";
    response += "<p>MAC地址: " + macAddress + "</p></body></html>";
  } else {
    sendErrorResponse(client, "激活码不正确，请重新输入");
    return;
  }
  
  client.print(response);
}

// 激活AP模式
void startActivationAP() {
  WiFi.apbegin(ACTIVATION_AP_SSID, ACTIVATION_AP_PASS, "1");
  activationServer.begin();
  
  Serial.println("激活AP已启动");
  Serial.print("SSID: ");
  Serial.println(ACTIVATION_AP_SSID);
  Serial.print("IP地址: ");
  //Serial.println(WiFi.apgetIP());
  
  // 在OLED上显示激活信息
  display.clearDisplay();
  u8g2Font.setCursor(0, 12);
  u8g2Font.print("激活AP: bw16激活");
  u8g2Font.setCursor(0, 24);
  u8g2Font.print("IP: ***********");
  u8g2Font.setCursor(0, 36);
  u8g2Font.print("密码：88888888");
  u8g2Font.setCursor(0, 48);
  u8g2Font.print("QQ群:552674356");
  display.display();
}

// 网络激活流程
void activateViaNetwork(String macAddress) {
  // 初始化安全变量
  activationStartTime = millis() / 1000;
  activationAttempts = 0;
  lastActivationAttempt = 0;
  
  startActivationAP();
  
  while (true) {
    WiFiClient client = activationServer.available();
    if (client) {
      String request = "";
      while (client.connected()) {
        if (client.available()) {
          char c = client.read();
          request += c;
          
          // 检测请求结束
          if (request.endsWith("\r\n\r\n")) {
            break;
          }
        }
      }
      
      // 处理请求
      if (request.indexOf("GET / ") != -1 || request.indexOf("GET /index") != -1) {
        // 添加charset=utf-8声明
        client.print("HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\n\r\n");
        client.print(generateActivationPage(macAddress));
      } 
      else if (request.indexOf("GET /refresh-captcha") != -1) {
        // 刷新验证码
        String newCaptcha = generateCaptcha();
        currentCaptcha = newCaptcha;
        client.print("HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=utf-8\r\n\r\n");
        client.print(newCaptcha);
      }
      else if (request.indexOf("POST /activate") != -1) {
        handleActivation(client, request, macAddress);
      } 
      else {
        client.print("HTTP/1.1 404 Not Found\r\n\r\n");
      }
      
      delay(10);
      client.stop();
    }
    
    // 检查激活状态
    String storedHash = readHashFromFlash();
    String expectedHash = multipleMD5(SALT + macAddress);
    if (storedHash.equals(expectedHash)) {
      display.clearDisplay();
      u8g2Font.setCursor(15, 20);
      u8g2Font.print("激活成功!");
      u8g2Font.setCursor(0, 40);
      u8g2Font.print("MAC:");
      u8g2Font.print(macAddress);
      display.display();
      delay(3000);
      break;
    }
    
    // 检查会话超时
    unsigned long currentTime = millis() / 1000;
    if (currentTime - activationStartTime > ACTIVATION_TIMEOUT) {
      // 重新生成验证码
      currentCaptcha = generateCaptcha();
      activationStartTime = currentTime;
    }
    
    delay(100);
  }
  
  // 关闭AP
  //WiFi.apend();
}

//初始化函数
void ProtectSetup() {
  Serial.begin(115200);
  FlashMemory.begin(HASH_ADDRESS, 0x4000);

  String macAddress = getMACAddress();
  String concatenatedMAC = SALT + macAddress;
  String expectedHash = multipleMD5(concatenatedMAC);
  String storedHash = readHashFromFlash();

  Serial.print("MAC地址: ");
  Serial.println(macAddress);

  if (storedHash == expectedHash) {
    Serial.println("设备已注册");
  } else {
    activateViaNetwork(macAddress);
  }
}

#endif //PROTECT_H