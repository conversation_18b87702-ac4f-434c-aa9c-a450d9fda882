#include "wifi_cust_tx.h"

/*
* 传输具有给定长度的原始 802.11 帧。
* 帧必须有效且序列号为 0，因为它将自动设置。
* 帧检查序列是自动添加的，不得包含在长度中。
* @param 帧 指向原始帧的指针
* @param尺寸 相框的尺寸
*/
void wifi_tx_raw_frame(void* frame, size_t length) {
  void *ptr = (void *)**(uint32_t **)(rltk_wlan_info + 0x10);
  void *frame_control = alloc_mgtxmitframe(ptr + 0xae0);

  if (frame_control != 0) {
    update_mgntframe_attrib(ptr, frame_control + 8);
    memset((void *)*(uint32_t *)(frame_control + 0x80), 0, 0x68);
    uint8_t *frame_data = (uint8_t *)*(uint32_t *)(frame_control + 0x80) + 0x28;
    memcpy(frame_data, frame, length);
    *(uint32_t *)(frame_control + 0x14) = length;
    *(uint32_t *)(frame_control + 0x18) = length;
    dump_mgntframe(ptr, frame_control);
  }
}

/*
* 在活动通道上传输 802.11 deauth 帧
* @param src_mac 包含发送方的 mac 地址的字节数组。数组的大小必须为 6 字节
* @param dst_mac 包含目标 mac 地址的字节数组或 FF：FF：FF：FF：FF：FF：FF 以广播身份验证
* @param原因 根据 802.11 规范的原因代码。自选
*/
void wifi_tx_deauth_frame(void* src_mac, void* dst_mac, uint16_t reason) {
  DeauthFrame frame;
  memcpy(&frame.source, src_mac, 6);
  memcpy(&frame.access_point, src_mac, 6);
  memcpy(&frame.destination, dst_mac, 6);
  frame.reason = reason;
  wifi_tx_raw_frame(&frame, sizeof(DeauthFrame));
}

/*
* 在活动信道上传输具有给定 ssid 的非常基本的 802.11 信标
* @param src_mac 包含发送方的 mac 地址的字节数组。数组的大小必须为 6 字节
* @param dst_mac 包含目标 mac 地址的字节数组或 FF：FF：FF：FF：FF：FF：FF 以广播信标
* @param SSID '0' 终止的字符数组，表示 SSID
*/
void wifi_tx_beacon_frame(void* src_mac, void* dst_mac, const char *ssid) {
  BeaconFrame frame;
  memcpy(&frame.source, src_mac, 6);
  memcpy(&frame.access_point, src_mac, 6);
  memcpy(&frame.destination, dst_mac, 6);
  for (int i = 0; ssid[i] != '\0'; i++) {
    frame.ssid[i] = ssid[i];
    frame.ssid_length++;
  }
  wifi_tx_raw_frame(&frame, 38 + frame.ssid_length);
}