<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>固件保护注册码生成器</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
</head>
<body>
<h1>注册码哈希生成器</h1>
<label for="macInput">请输入MAC地址 (例如: A1B2C3D4E5F6)</label><br>
<input type="text" id="textInput" placeholder="请输入要哈希的文本">
<button onclick="computeCustomMD5()">计算哈希值</button>
<p id="result"></p>

<script>
function computeCustomMD5() {
// 获取输入值
var input = document.getElementById('textInput').value;

// 与盐值连接
var concatenatedInput = "SALTYFISH" + input;

// 执行10次MD5哈希
var currentHash = concatenatedInput;
for (var i = 0; i < 10; i++) {
    currentHash = CryptoJS.MD5(currentHash).toString();
}

// 显示结果
document.getElementById('result').textContent = '注册码: ' + currentHash;
}
</script>
</body>
</html>


